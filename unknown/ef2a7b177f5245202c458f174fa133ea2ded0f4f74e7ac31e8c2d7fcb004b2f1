import { OpenAPIHono, create<PERSON>out<PERSON>, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import auctionExtensionController from "../controllers/auctionExtension.controller";

const auctionExtensionRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// Schema definitions
const extensionLogSchema = z.object({
  id: z.string(),
  productId: z.string(),
  previousEndDate: z.string(),
  newEndDate: z.string(),
  extendedMinutes: z.number(),
  triggerBidAmount: z.number(),
  triggeredBy: z.enum(['manual', 'auto-bid']),
  triggeredBidder: z.object({
    id: z.string(),
    firstName: z.string().nullable(),
    lastName: z.string().nullable(),
    email: z.string()
  }).nullable(),
  extensionReason: z.string(),
  createdAt: z.string()
});

const extensionStatsSchema = z.object({
  productId: z.string(),
  totalExtensions: z.number(),
  totalExtendedMinutes: z.number(),
  averageTriggerBid: z.number(),
  highestTriggerBid: z.number(),
  extensionsByType: z.array(z.object({
    triggeredBy: z.string(),
    count: z.number()
  }))
});

// Get extension logs for a specific product
const getProductExtensionLogsRoute = createRoute({
  method: "get",
  path: "/product/{productId}/logs",
  request: {
    params: z.object({
      productId: z.string().uuid("Invalid product ID"),
    }),
  },
  responses: {
    200: {
      description: "Extension logs retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              productId: z.string(),
              extensionLogs: z.array(extensionLogSchema),
              totalExtensions: z.number()
            }),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    404: {
      description: "Product not found",
    },
  },
});

// Get all extension logs with pagination
const getAllExtensionLogsRoute = createRoute({
  method: "get",
  path: "/logs",
  request: {
    query: z.object({
      page: z.string().optional(),
      limit: z.string().optional(),
      productId: z.string().uuid().optional(),
      triggeredBy: z.enum(['manual', 'auto-bid']).optional(),
    }),
  },
  responses: {
    200: {
      description: "Extension logs retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              extensionLogs: z.array(extensionLogSchema.extend({
                product: z.object({
                  id: z.string(),
                  itemName: z.string(),
                  slug: z.string().nullable()
                })
              })),
              pagination: z.object({
                currentPage: z.number(),
                totalPages: z.number(),
                totalCount: z.number(),
                limit: z.number(),
                hasNext: z.boolean(),
                hasPrev: z.boolean()
              })
            }),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
  },
});

// Get extension statistics for a product
const getProductExtensionStatsRoute = createRoute({
  method: "get",
  path: "/product/{productId}/stats",
  request: {
    params: z.object({
      productId: z.string().uuid("Invalid product ID"),
    }),
  },
  responses: {
    200: {
      description: "Extension statistics retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: extensionStatsSchema,
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    404: {
      description: "Product not found",
    },
  },
});

// Debug route for extend bidding
const debugProductExtendBiddingRoute = createRoute({
  method: 'get',
  path: '/product/{productId}/debug',
  summary: 'Debug product extend bidding settings',
  request: {
    params: z.object({
      productId: z.string().openapi({
        param: {
          name: 'productId',
          in: 'path',
        },
        example: '123e4567-e89b-12d3-a456-************',
      }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any()
          }),
        },
      },
      description: 'Debug info retrieved successfully',
    },
    400: {
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: 'Bad request',
    },
    500: {
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: 'Internal server error',
    },
  },
});

// Test route for extend bidding
const testExtendBiddingRoute = createRoute({
  method: 'post',
  path: '/product/{productId}/test-extend',
  summary: 'Test extend bidding functionality',
  request: {
    params: z.object({
      productId: z.string().openapi({
        param: {
          name: 'productId',
          in: 'path',
        },
        example: '123e4567-e89b-12d3-a456-************',
      }),
    }),
    body: {
      content: {
        'application/json': {
          schema: z.object({
            bidAmount: z.number(),
            bidderId: z.string().optional()
          }),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any()
          }),
        },
      },
      description: 'Test completed successfully',
    },
    400: {
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: 'Bad request',
    },
    500: {
      content: {
        'application/json': {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: 'Internal server error',
    },
  },
});

// Register routes (these are public routes for now, add auth middleware if needed)
auctionExtensionRoutes.openapi(getProductExtensionLogsRoute, auctionExtensionController.getProductExtensionLogs);
auctionExtensionRoutes.openapi(getAllExtensionLogsRoute, auctionExtensionController.getAllExtensionLogs);
auctionExtensionRoutes.openapi(getProductExtensionStatsRoute, auctionExtensionController.getProductExtensionStats);
auctionExtensionRoutes.openapi(debugProductExtendBiddingRoute, auctionExtensionController.debugProductExtendBidding);
auctionExtensionRoutes.openapi(testExtendBiddingRoute, auctionExtensionController.testExtendBidding);

export { auctionExtensionRoutes };
