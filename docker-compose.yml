version: "3.8"

services:
  app:
    image: reihanpraja/king-collectible:latest
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env.production
    environment:
      - NODE_ENV=production
      - TZ=Asia/Jakarta
      - NEXT_PUBLIC_WS_URL=ws://YOUR_PUBLIC_IP:3001
      - WS_PORT=3001
    depends_on:
      db:
        condition: service_healthy
      # websocket:
      #   condition: service_healthy  # Remove dependency for now
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  websocket:
    image: reihanpraja/king-collectible:latest
    restart: unless-stopped
    ports:
      - "3001:3001"  # Direct access for testing
    env_file:
      - .env.production
    environment:
      - NODE_ENV=production
      - TZ=Asia/Jakarta
      - WS_PORT=3001
      - SERVICE_TYPE=websocket
    # depends_on:
    #   db:
    #     condition: service_healthy  # Remove DB dependency for testing
    command: ["npm", "run", "start:ws:dev"]  # Use tsx directly for debugging
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 90s
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx reverse proxy for HTTP and WebSocket upgrade (disabled for testing)
  # nginx:
  #   image: nginx:alpine
  #   restart: unless-stopped
  #   ports:
  #     - "80:80"
  #     - "3001:3001"
  #   volumes:
  #     - ${PWD}/nginx.conf:/etc/nginx/nginx.conf:ro
  #   depends_on:
  #     - app
  #     - websocket
  #   networks:
  #     - app-network
  #   logging:
  #     driver: "json-file"
  #     options:
  #       max-size: "10m"
  #       max-file: "3"

  db:
    image: mysql:8.0
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      TZ: Asia/Jakarta
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=512M
      --max-connections=500
      --wait-timeout=28800
      --interactive-timeout=28800
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_ROOT_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 30s
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  app-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
