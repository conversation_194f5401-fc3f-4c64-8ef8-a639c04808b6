import { z } from "zod";

// Order status enums
export const orderStatusEnum = z.enum(["pending", "processing", "shipped", "delivered", "cancelled"]);
export const paymentStatusEnum = z.enum(["pending", "paid", "failed", "refunded"]);
export const paymentMethodEnum = z.enum(["credit_card", "debit_card", "paypal", "bank_transfer"]);

// Shipping address schema
export const shippingAddressSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  address: z.string(),
  city: z.string(),
  provinceRegion: z.string(),
  zipCode: z.string(),
  country: z.string(),
});

// Product in order schema
export const productInOrderSchema = z.object({
  id: z.string().uuid(),
  itemName: z.string(),
  slug: z.string().nullable(),
  priceUSD: z.number(),
  sellType: z.enum(["auction", "buy-now"]),
  status: z.string(),
  images: z.array(z.object({
    id: z.string().uuid(),
    imageUrl: z.string(),
    altText: z.string().nullable(),
    sortOrder: z.number().int(),
    isMain: z.boolean(),
  })),
});

// Order item schema
export const orderItemSchema = z.object({
  id: z.string().uuid(),
  orderId: z.string().uuid(),
  productId: z.string().uuid(),
  quantity: z.number().int(),
  price: z.number(),
  bidId: z.string().uuid().nullable(),
  product: productInOrderSchema,
  createdAt: z.string().datetime(),
});

// Order response schema
export const orderResponseSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  orderNumber: z.string(),
  status: orderStatusEnum,
  paymentStatus: paymentStatusEnum,
  paymentMethod: paymentMethodEnum.nullable(),
  subtotal: z.number(),
  shippingCost: z.number(),
  tax: z.number(),
  total: z.number(),
  shippingAddressId: z.string().uuid().nullable(),
  bidId: z.string().uuid().nullable(),
  notes: z.string().nullable(),
  shippingAddress: shippingAddressSchema.nullable(),
  items: z.array(orderItemSchema),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Orders list response schema
export const ordersListResponseSchema = z.object({
  orders: z.array(orderResponseSchema),
  pagination: z.object({
    page: z.number().int(),
    limit: z.number().int(),
    total: z.number().int(),
    totalPages: z.number().int(),
  }),
});

// Query schema for orders
export const ordersQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().int().min(1)).default("1"),
  limit: z.string().transform(Number).pipe(z.number().int().min(1).max(100)).default("10"),
  status: orderStatusEnum.optional(),
  paymentStatus: paymentStatusEnum.optional(),
  sortBy: z.enum(["createdAt", "total", "status", "orderNumber"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Update order status schema
export const updateOrderStatusSchema = z.object({
  status: orderStatusEnum.optional(),
  paymentStatus: paymentStatusEnum.optional(),
});

// Export types
export type OrderStatus = z.infer<typeof orderStatusEnum>;
export type PaymentStatus = z.infer<typeof paymentStatusEnum>;
export type PaymentMethod = z.infer<typeof paymentMethodEnum>;
export type ShippingAddress = z.infer<typeof shippingAddressSchema>;
export type ProductInOrder = z.infer<typeof productInOrderSchema>;
export type OrderItem = z.infer<typeof orderItemSchema>;
export type OrderResponse = z.infer<typeof orderResponseSchema>;
export type OrdersListResponse = z.infer<typeof ordersListResponseSchema>;
export type OrdersQuery = z.infer<typeof ordersQuerySchema>;
export type UpdateOrderStatusData = z.infer<typeof updateOrderStatusSchema>;
