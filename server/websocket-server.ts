import { createServer } from "http";
import { parse } from "url";
import webSocketService from "./services/websocket.service";

const PORT = process.env.WS_PORT || 3001;

// Create HTTP server for WebSocket
const server = createServer((req, res) => {
  const parsedUrl = parse(req.url || "", true);

  // Handle health check endpoint
  if (parsedUrl.pathname === "/health" || parsedUrl.pathname === "/ws-health") {
    res.writeHead(200, {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    });

    const healthData = {
      status: "healthy",
      service: "websocket",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || "development",
      port: PORT,
      connections: webSocketService.getConnectionStats(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
      },
    };

    res.end(JSON.stringify(healthData, null, 2));
    return;
  }

  // Handle metrics endpoint
  if (parsedUrl.pathname === "/metrics") {
    res.writeHead(200, {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    });

    const metrics = webSocketService.getDetailedMetrics();
    res.end(JSON.stringify(metrics, null, 2));
    return;
  }

  // Handle WebSocket notification endpoints
  if (req.method === "POST" && parsedUrl.pathname === "/notify/bid") {
    let body = "";
    req.on("data", (chunk) => {
      body += chunk.toString();
    });
    req.on("end", () => {
      try {
        const data = JSON.parse(body);
        console.log("📨 Received bid notification request:", data);

        webSocketService.notifyNewBid(data.productId, data.bidData);

        res.writeHead(200, {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        });
        res.end(
          JSON.stringify({ success: true, message: "Bid notification sent" })
        );
      } catch (error) {
        console.error("❌ Error processing bid notification:", error);
        res.writeHead(400, { "Content-Type": "application/json" });
        res.end(
          JSON.stringify({ success: false, error: "Invalid request body" })
        );
      }
    });
    return;
  }

  if (req.method === "POST" && parsedUrl.pathname === "/notify/auction-ended") {
    let body = "";
    req.on("data", (chunk) => {
      body += chunk.toString();
    });
    req.on("end", () => {
      try {
        const data = JSON.parse(body);
        console.log("📨 Received auction end notification:", data);
        webSocketService.notifyAuctionEnded(data.productId, data.winnerData);
      } catch (error) {
        console.error("❌ Error processing auction end notification:", error);
        res.writeHead(400, { "Content-Type": "application/json" });
        res.end(
          JSON.stringify({ success: false, error: "Invalid request body" })
        );
      }
    });
    return;
  }

  if (req.method === "POST" && parsedUrl.pathname === "/notify/auto-bid") {
    let body = "";
    req.on("data", (chunk) => {
      body += chunk.toString();
    });
    req.on("end", () => {
      try {
        const data = JSON.parse(body);
        console.log("📨 Received auto-bid notification:", data);

        webSocketService.notifyAutoBid(data.productId, data.autoBidData);

        res.writeHead(200, {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        });
        res.end(
          JSON.stringify({
            success: true,
            message: "Auto-bid notification sent",
          })
        );
      } catch (error) {
        console.error("❌ Error processing auto-bid notification:", error);
        res.writeHead(400, { "Content-Type": "application/json" });
        res.end(
          JSON.stringify({ success: false, error: "Invalid request body" })
        );
      }
    });
    return;
  }

  if (
    req.method === "POST" &&
    parsedUrl.pathname === "/notify/auction-extended"
  ) {
    let body = "";
    req.on("data", (chunk) => {
      body += chunk.toString();
    });
    req.on("end", () => {
      try {
        const data = JSON.parse(body);
        console.log("📨 Received auction extension notification:", data);

        webSocketService.notifyAuctionExtended(
          data.productId,
          data.extensionData
        );

        res.writeHead(200, {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        });
        res.end(
          JSON.stringify({
            success: true,
            message: "Auction extension notification sent",
          })
        );
      } catch (error) {
        console.error(
          "❌ Error processing auction extension notification:",
          error
        );
        res.writeHead(400, { "Content-Type": "application/json" });
        res.end(
          JSON.stringify({ success: false, error: "Invalid request body" })
        );
      }
    });
    return;
  }

  // Handle OPTIONS requests for CORS
  if (req.method === "OPTIONS") {
    res.writeHead(200, {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    });
    res.end();
    return;
  }

  // For other requests, return 404
  res.writeHead(404, { "Content-Type": "text/plain" });
  res.end("Not Found");
});

// Initialize WebSocket service
webSocketService.initialize(server);

// Start server
server.listen(PORT, () => {
  console.log(`🚀 WebSocket server running on port ${PORT}`);
  console.log(`📡 WebSocket URL: ws://localhost:${PORT}`);
});

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("🛑 Shutting down WebSocket server...");
  webSocketService.close();
  server.close(() => {
    console.log("✅ WebSocket server closed");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  console.log("🛑 Shutting down WebSocket server...");
  webSocketService.close();
  server.close(() => {
    console.log("✅ WebSocket server closed");
    process.exit(0);
  });
});

export default server;
