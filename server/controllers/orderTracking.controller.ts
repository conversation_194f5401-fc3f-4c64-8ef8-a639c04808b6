import { Context } from 'hono';
import orderTrackingService from '../services/orderTracking.service';
import { errorResponse } from '../utils/response.util';

class OrderTrackingController {
  /**
   * Get order tracking information
   */
  async getOrderTracking(c: Context) {
    try {
      const orderId = c.req.param('orderId');
      const user = c.get('user');

      if (!orderId) {
        return c.json(errorResponse("Order ID is required"), 400);
      }

      // Require authentication for order tracking
      if (!user) {
        return c.json(errorResponse("Authentication required"), 401);
      }

      const result = await orderTrackingService.getOrderTracking(orderId, user.id);

      if (!result.status) {
        // Handle specific error messages
        if (result.message === "Order not found") {
          return c.json(result, 404);
        } else if (result.message === "Unauthorized to view this order") {
          return c.json(result, 403);
        } else {
          return c.json(result, 400);
        }
      }

      return c.json(result);
    } catch (error) {
      console.error('Get order tracking controller error:', error);
      return c.json(errorResponse("An error occurred while getting order tracking"), 500);
    }
  }

  /**
   * Get user's orders with tracking
   */
  async getUserOrders(c: Context) {
    try {
      const user = c.get('user');
      
      if (!user) {
        return c.json(errorResponse("Authentication required"), 401);
      }

      const page = parseInt(c.req.query('page') || '1');
      const limit = parseInt(c.req.query('limit') || '10');

      if (page < 1 || limit < 1 || limit > 100) {
        return c.json(errorResponse("Invalid pagination parameters"), 400);
      }

      const result = await orderTrackingService.getUserOrders(user.id, page, limit);
      
      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result);
    } catch (error) {
      console.error('Get user orders controller error:', error);
      return c.json(errorResponse("An error occurred while getting user orders"), 500);
    }
  }

  /**
   * Update order status (admin/seller only)
   */
  async updateOrderStatus(c: Context) {
    try {
      const user = c.get('user');
      const orderId = c.req.param('orderId');
      const body = await c.req.json();
      
      if (!user) {
        return c.json(errorResponse("Authentication required"), 401);
      }

      if (!orderId) {
        return c.json(errorResponse("Order ID is required"), 400);
      }

      const { status, description } = body;

      if (!status) {
        return c.json(errorResponse("Status is required"), 400);
      }

      // Validate status
      const validStatuses = ['pending_payment', 'paid', 'seller_confirmed', 'shipped', 'delivered', 'cancelled'];
      if (!validStatuses.includes(status)) {
        return c.json(errorResponse("Invalid status"), 400);
      }

      // Check if user has permission to update this order
      // For now, allow admin and the order owner
      if (user.role !== 'admin') {
        // Check if user owns this order
        const orderCheck = await orderTrackingService.getOrderTracking(orderId, user.id);
        if (!orderCheck.status) {
          return c.json(errorResponse("Unauthorized to update this order"), 403);
        }
      }

      const result = await orderTrackingService.updateOrderStatus({
        orderId,
        status,
        description,
        createdBy: user.id
      });
      
      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result);
    } catch (error) {
      console.error('Update order status controller error:', error);
      return c.json(errorResponse("An error occurred while updating order status"), 500);
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(c: Context) {
    try {
      const user = c.get('user');
      const orderId = c.req.param('orderId');
      const body = await c.req.json();
      
      if (!user) {
        return c.json(errorResponse("Authentication required"), 401);
      }

      if (!orderId) {
        return c.json(errorResponse("Order ID is required"), 400);
      }

      const { reason } = body;

      const result = await orderTrackingService.cancelOrder(orderId, user.id, reason);
      
      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result);
    } catch (error) {
      console.error('Cancel order controller error:', error);
      return c.json(errorResponse("An error occurred while cancelling order"), 500);
    }
  }

  /**
   * Create order with tracking (called from checkout)
   */
  async createOrderWithTracking(c: Context) {
    try {
      const user = c.get('user');
      const body = await c.req.json();
      
      if (!user) {
        return c.json(errorResponse("Authentication required"), 401);
      }

      // Validate required fields
      const { items, shippingAddressId, paymentMethod, currency, subtotal, shippingCost, tax, total } = body;

      if (!items || !Array.isArray(items) || items.length === 0) {
        return c.json(errorResponse("Order items are required"), 400);
      }

      if (!shippingAddressId) {
        return c.json(errorResponse("Shipping address is required"), 400);
      }

      if (!paymentMethod) {
        return c.json(errorResponse("Payment method is required"), 400);
      }

      if (!total || total <= 0) {
        return c.json(errorResponse("Invalid order total"), 400);
      }

      const orderData = {
        userId: user.id,
        items,
        shippingAddressId,
        paymentMethod,
        currency: currency || 'USD',
        subtotal: subtotal || 0,
        shippingCost: shippingCost || 0,
        tax: tax || 0,
        total,
        notes: body.notes
      };

      const result = await orderTrackingService.createOrderWithTracking(orderData);
      
      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result);
    } catch (error) {
      console.error('Create order with tracking controller error:', error);
      return c.json(errorResponse("An error occurred while creating order"), 500);
    }
  }

  /**
   * Get order statistics (admin only)
   */
  async getOrderStatistics(c: Context) {
    try {
      const user = c.get('user');
      
      if (!user || user.role !== 'admin') {
        return c.json(errorResponse("Admin access required"), 403);
      }

      // This would be implemented in the service
      // For now, return a placeholder
      return c.json({
        status: true,
        message: "Order statistics retrieved successfully",
        data: {
          totalOrders: 0,
          pendingOrders: 0,
          shippedOrders: 0,
          deliveredOrders: 0,
          cancelledOrders: 0,
          totalRevenue: 0
        }
      });
    } catch (error) {
      console.error('Get order statistics controller error:', error);
      return c.json(errorResponse("An error occurred while getting order statistics"), 500);
    }
  }

  /**
   * Search orders (admin only)
   */
  async searchOrders(c: Context) {
    try {
      const user = c.get('user');
      
      if (!user || user.role !== 'admin') {
        return c.json(errorResponse("Admin access required"), 403);
      }

      const query = c.req.query('q');
      const page = parseInt(c.req.query('page') || '1');
      const limit = parseInt(c.req.query('limit') || '20');

      if (!query) {
        return c.json(errorResponse("Search query is required"), 400);
      }

      // This would be implemented in the service
      // For now, return a placeholder
      return c.json({
        status: true,
        message: "Orders search completed",
        data: {
          orders: [],
          pagination: {
            page,
            limit,
            total: 0,
            totalPages: 0
          }
        }
      });
    } catch (error) {
      console.error('Search orders controller error:', error);
      return c.json(errorResponse("An error occurred while searching orders"), 500);
    }
  }
}

export default new OrderTrackingController();
