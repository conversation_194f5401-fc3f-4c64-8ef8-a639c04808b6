import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import auctionService from "../services/auction.service";

class AuctionController {
  /**
   * Check if the authenticated user won a specific auction
   */
  async checkWinnerByProduct(c: Context) {
    try {
      const productId = c.req.param('productId');
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      const result = await auctionService.checkWinnerByProduct(productId, user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Check winner by product controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  /**
   * Get auction winner information for a product (public endpoint)
   */
  async getAuctionWinner(c: Context) {
    try {
      const productId = c.req.param('productId');

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      const result = await auctionService.getAuctionWinner(productId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get auction winner controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

export default new AuctionController();
