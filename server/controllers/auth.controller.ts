import { Context } from "hono";
import { errorResponse, successResponse } from "../utils/response.util";
import authService from "../services/auth.service";

class AuthController {
  async login(c: Context) {
    try {
      const body = await c.req.json();
      const { email, password } = body;

      const login = await authService.login({ email, password });
      if (!login.status) {
        return c.json(login, 400);
      }

      return c.json(successResponse("Login successful", login.data), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async register(c: Context) {
    try {
      const body = await c.req.json();
      const {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        confirmPassword,
      } = body;

      const register = await authService.register({
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        confirmPassword,
      })

      if (!register.status) {
        return c.json(register, 400);
      }

      return c.json(register, 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async profile(c: Context) {
    try {
      const user = c.get("user");
      if (!user) {
        return c.json(errorResponse("User not found"), 404);
      }

      const profile = await authService.getProfile(user.id);
      if (!profile.status) {
        return c.json(profile, 404);
      }

      return c.json(successResponse("Profile retrieved successfully", profile.data), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async refreshToken(c: Context) {
    try {
      const body = await c.req.json();
      const { refreshToken } = body;

      if (!refreshToken) {
        return c.json(errorResponse("Refresh token is required"), 400);
      }

      const result = await authService.refreshToken(refreshToken);
      if (!result.status) {
        return c.json(result, 401);
      }

      return c.json(successResponse("Token refreshed successfully", result.data), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async googleAuth(c: Context) {
    try {
      const body = await c.req.json();
      const { googleToken } = body;

      if (!googleToken) {
        return c.json(errorResponse("Google token is required"), 400);
      }

      const result = await authService.googleAuth(googleToken);
      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(successResponse("Google authentication successful", result.data), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async logout(c: Context) {
    try {
      const user = c.get('user');
      const authHeader = c.req.header('Authorization');

      // Extract JWT ID from token if available
      let jti: string | undefined;
      if (authHeader) {
        const token = authHeader.replace('Bearer ', '');
        try {
          const { verify } = await import('hono/jwt');
          const payload = await verify(token, process.env.JWT_SECRET!) as any;
          jti = payload.jti;
        } catch (error) {
          // Token might be invalid, but we can still logout
          console.warn('Could not extract JTI from token during logout:', error);
        }
      }

      const result = await authService.logout(user.id, jti);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(successResponse("Logged out successfully"), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async logoutAll(c: Context) {
    try {
      const user = c.get('user');
      const result = await authService.logoutAll(user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(successResponse("Logged out from all devices successfully"), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

export default new AuthController();
