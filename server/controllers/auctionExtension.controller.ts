import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import auctionExtensionService from "../services/auctionExtension.service";

class AuctionExtensionController {
  // Get extension logs for a specific product
  async getProductExtensionLogs(c: Context) {
    try {
      const productId = c.req.param('productId');

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      const result = await auctionExtensionService.getProductExtensionLogs(productId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get product extension logs controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  // Get all extension logs with pagination and filters
  async getAllExtensionLogs(c: Context) {
    try {
      const rawQuery = c.req.query();
      const query = {
        page: parseInt(rawQuery.page || '1'),
        limit: parseInt(rawQuery.limit || '20'),
        productId: rawQuery.productId,
        triggeredBy: rawQuery.triggeredBy as 'manual' | 'auto-bid' | undefined
      };

      const result = await auctionExtensionService.getAllExtensionLogs(query);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get all extension logs controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  // Get extension statistics for a product
  async getProductExtensionStats(c: Context) {
    try {
      const productId = c.req.param('productId');

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      const result = await auctionExtensionService.getProductExtensionStats(productId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get product extension stats controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  // Debug endpoint to check product extend bidding settings
  async debugProductExtendBidding(c: Context) {
    try {
      const productId = c.req.param('productId');

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      const result = await auctionExtensionService.debugProductExtendBidding(productId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Debug product extend bidding controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  // Test endpoint to simulate extend bidding
  async testExtendBidding(c: Context) {
    try {
      const productId = c.req.param('productId');
      const body = await c.req.json();
      const { bidAmount, bidderId } = body;

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      if (!bidAmount) {
        return c.json(errorResponse("Bid amount is required"), 400);
      }

      const result = await auctionExtensionService.testExtendBidding(productId, bidAmount, bidderId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Test extend bidding controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

const auctionExtensionController = new AuctionExtensionController();
export default auctionExtensionController;
