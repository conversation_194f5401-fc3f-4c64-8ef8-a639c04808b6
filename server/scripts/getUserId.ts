import { prisma } from "../db";

async function getUserId() {
  try {
    const user = await prisma.user.findFirst({
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true
      }
    });

    if (user) {
      console.log("✅ Found user:");
      console.log(`   - ID: ${user.id}`);
      console.log(`   - Name: ${user.firstName} ${user.lastName}`);
      console.log(`   - Email: ${user.email}`);
    } else {
      console.log("❌ No users found in database");
    }
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await prisma.$disconnect();
  }
}

getUserId();
