import { prisma } from "../db";
import { getJakartaTime } from "../utils/timezone.util";

/**
 * Update auction end date to be within extend bidding trigger window for testing
 */

async function updateAuctionEndDate() {
  console.log("🔧 Updating auction end date for testing extend bidding...");

  try {
    const productId = "680316b0-7b5c-4eb2-9635-4d782f35ea0d";
    
    // Set auction end date to 8 minutes from now (within the 10-minute trigger window)
    const now = getJakartaTime();
    const newEndDate = new Date(now.getTime() + (8 * 60 * 1000)); // 8 minutes from now
    
    console.log(`📅 Current time: ${now.toISOString()}`);
    console.log(`📅 New auction end date: ${newEndDate.toISOString()}`);
    console.log(`⏰ Time until auction ends: 8 minutes`);

    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: {
        auctionEndDate: newEndDate,
        auctionCompleted: false // Reset auction completed status
      },
      select: {
        id: true,
        itemName: true,
        auctionEndDate: true,
        extendedBiddingEnabled: true,
        extendedBiddingMinutes: true,
        extendedBiddingDuration: true
      }
    });
    
    console.log("✅ Product updated successfully:");
    console.log(`   - Product: ${updatedProduct.itemName}`);
    console.log(`   - New end date: ${updatedProduct.auctionEndDate}`);
    console.log(`   - Extend bidding enabled: ${updatedProduct.extendedBiddingEnabled}`);
    console.log(`   - Trigger minutes: ${updatedProduct.extendedBiddingMinutes}`);
    console.log(`   - Extension duration: ${updatedProduct.extendedBiddingDuration}`);
    
    console.log("\n🎯 Now you can test extend bidding by placing a bid within the next 8 minutes!");
    
  } catch (error) {
    console.error("❌ Failed to update auction end date:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
updateAuctionEndDate()
  .then(() => {
    console.log("✅ Script completed successfully!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
