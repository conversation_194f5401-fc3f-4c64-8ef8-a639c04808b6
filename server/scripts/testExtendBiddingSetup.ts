import { prisma } from "../db";

/**
 * Simple test to verify the extend bidding database setup is working
 */

async function testExtendBiddingSetup() {
  console.log("🔍 Testing Extend Bidding Setup...");

  try {
    // Test 1: Check if AuctionExtensionLog table exists and is accessible
    console.log("\n📊 Test 1: Checking AuctionExtensionLog table...");
    const extensionCount = await prisma.auctionExtensionLog.count();
    console.log(`✅ AuctionExtensionLog table accessible. Current records: ${extensionCount}`);

    // Test 2: Check if we have auction products with extend bidding enabled
    console.log("\n🏷️  Test 2: Checking auction products with extend bidding...");
    const auctionProducts = await prisma.product.findMany({
      where: {
        sellType: 'auction',
        extendedBiddingEnabled: true
      },
      select: {
        id: true,
        itemName: true,
        extendedBiddingEnabled: true,
        extendedBiddingMinutes: true,
        extendedBiddingDuration: true,
        auctionEndDate: true
      },
      take: 5
    });

    console.log(`✅ Found ${auctionProducts.length} auction products with extend bidding enabled:`);
    auctionProducts.forEach(product => {
      console.log(`   - ${product.itemName} (${product.id})`);
      console.log(`     Trigger: ${product.extendedBiddingMinutes}min, Duration: ${product.extendedBiddingDuration}min`);
    });

    // Test 3: Check if we can query extension logs with relations
    console.log("\n🔗 Test 3: Testing extension log relations...");
    const extensionLogs = await prisma.auctionExtensionLog.findMany({
      include: {
        product: {
          select: {
            id: true,
            itemName: true
          }
        },
        triggeredBidder: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      },
      take: 3
    });

    console.log(`✅ Found ${extensionLogs.length} extension logs with relations working correctly`);

    // Test 4: Verify the extend bidding logic can be called (without actually extending)
    console.log("\n⚙️  Test 4: Testing extend bidding logic availability...");
    
    if (auctionProducts.length > 0) {
      const testProduct = auctionProducts[0];
      
      // Check if product has the required fields for extend bidding
      const hasRequiredFields = testProduct.extendedBiddingEnabled !== null &&
                               testProduct.extendedBiddingMinutes !== null &&
                               testProduct.extendedBiddingDuration !== null;
      
      if (hasRequiredFields) {
        console.log(`✅ Product ${testProduct.itemName} has all required extend bidding fields`);
        console.log(`   - Enabled: ${testProduct.extendedBiddingEnabled}`);
        console.log(`   - Trigger Minutes: ${testProduct.extendedBiddingMinutes}`);
        console.log(`   - Extension Duration: ${testProduct.extendedBiddingDuration}`);
      } else {
        console.log(`⚠️  Product ${testProduct.itemName} missing some extend bidding fields`);
      }
    } else {
      console.log("ℹ️  No auction products found to test extend bidding logic");
    }

    console.log("\n🎉 All tests completed successfully!");
    console.log("\n📋 Summary:");
    console.log(`   - AuctionExtensionLog table: ✅ Working`);
    console.log(`   - Auction products with extend bidding: ${auctionProducts.length} found`);
    console.log(`   - Extension logs: ${extensionCount} records`);
    console.log(`   - Database relations: ✅ Working`);

  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
if (require.main === module) {
  testExtendBiddingSetup()
    .then(() => {
      console.log("\n✅ Extend bidding setup test completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 Extend bidding setup test failed:", error);
      process.exit(1);
    });
}

export { testExtendBiddingSetup };
