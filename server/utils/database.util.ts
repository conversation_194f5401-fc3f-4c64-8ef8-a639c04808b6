import { PrismaClient } from '../../generated/client';
import { getJakartaTime, toJakartaTime, jakartaToUTC, utcToJakarta } from './timezone.util';

/**
 * Database utility functions for handling timezone conversions
 */

/**
 * Convert date to UTC before saving to database
 */
export const toUTCForDB = (date: Date | string): Date => {
  return jakartaToUTC(date);
};

/**
 * Convert date from UTC to Jakarta timezone after reading from database
 */
export const fromUTCFromDB = (date: Date | string): Date => {
  return utcToJakarta(date);
};

/**
 * Create Prisma client with timezone configuration
 */
export const createPrismaClient = (): PrismaClient => {
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });
};

/**
 * Middleware to handle timezone conversion for Prisma
 */
export const timezoneMiddleware = (prisma: PrismaClient) => {
  // Middleware to convert dates to UTC before saving
  prisma.$use(async (params, next) => {
    // Convert Jakarta time to UTC for create operations
    if (params.action === 'create' || params.action === 'update') {
      if (params.args.data) {
        convertDatesToUTC(params.args.data);
      }
    }

    // Convert Jakarta time to UTC for createMany operations
    if (params.action === 'createMany' && params.args.data) {
      if (Array.isArray(params.args.data)) {
        params.args.data.forEach((item: any) => convertDatesToUTC(item));
      }
    }

    const result = await next(params);

    // Convert UTC to Jakarta time for read operations
    if (params.action === 'findFirst' || params.action === 'findUnique') {
      if (result) {
        convertDatesFromUTC(result);
      }
    }

    if (params.action === 'findMany') {
      if (Array.isArray(result)) {
        result.forEach((item: any) => convertDatesFromUTC(item));
      }
    }

    return result;
  });
};

/**
 * Convert date fields to UTC in an object
 */
function convertDatesToUTC(obj: any): void {
  if (!obj || typeof obj !== 'object') return;

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      
      // Check if it's a Date object or date string
      if (value instanceof Date) {
        obj[key] = jakartaToUTC(value);
      } else if (typeof value === 'string' && isDateString(value)) {
        obj[key] = jakartaToUTC(value);
      } else if (typeof value === 'object' && value !== null) {
        // Recursively convert nested objects
        convertDatesToUTC(value);
      }
    }
  }
}

/**
 * Convert date fields from UTC to Jakarta timezone in an object
 */
function convertDatesFromUTC(obj: any): void {
  if (!obj || typeof obj !== 'object') return;

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      
      // Check if it's a Date object
      if (value instanceof Date) {
        obj[key] = utcToJakarta(value);
      } else if (typeof value === 'object' && value !== null) {
        // Recursively convert nested objects
        convertDatesFromUTC(value);
      }
    }
  }
}

/**
 * Check if a string is a valid date string
 */
function isDateString(str: string): boolean {
  // Check for ISO date format
  const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
  return isoDateRegex.test(str) && !isNaN(Date.parse(str));
}

/**
 * Get current timestamp in Jakarta timezone for database operations
 */
export const getCurrentTimestampForDB = (): Date => {
  return jakartaToUTC(getJakartaTime());
};

/**
 * Create date range query for Jakarta timezone
 */
export const createDateRangeQuery = (
  startDate: Date | string,
  endDate: Date | string
) => {
  return {
    gte: jakartaToUTC(startDate),
    lte: jakartaToUTC(endDate),
  };
};

/**
 * Create date query for today in Jakarta timezone
 */
export const createTodayQuery = () => {
  const jakartaTime = getJakartaTime();
  const startOfDay = new Date(jakartaTime);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(jakartaTime);
  endOfDay.setHours(23, 59, 59, 999);
  
  return {
    gte: jakartaToUTC(startOfDay),
    lte: jakartaToUTC(endOfDay),
  };
};
