import { OpenAPIHono, createRoute, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import { loginSchema, registerSchema, refreshTokenSchema, googleAuthSchema } from "../schemas/auth.schema";
import authController from "../controllers/auth.controller";
import { authMiddleware, rateLimitMiddleware } from "../middlewares/auth";


const authRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// authRoutes.use('*', rateLimitMiddleware(50, 15 * 60 * 1000)); 

const loginRoute = createRoute({
  method: "post",
  path: "/login",
  request: {
    body: {
      content: {
        "application/json": {
          schema: loginSchema.openapi("LoginSchema", {
            example: {
              email: "<EMAIL>",
              password: "password123",
            },
          }),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Login successful",
      content: {
        "application/json": {
          schema: z.object({
            message: z.string().describe("Success message"),
            token: z.string().describe("JWT token for authentication"),
          }),
        }
      }
    },
    400: {
      description: "Registration failed",
    },
  },
});


const registerRoute = createRoute({
  method: "post",
  path: "/register",
  request: {
    body: {
      content: {
        "application/json": {
          schema: registerSchema.openapi("RegisterSchema", {
            example: {
              firstName: "John",
              lastName: "Doe",
              email: "<EMAIL>",
              phoneNumber: "(62) 812345678",
              password: "password123",
              confirmPassword: "password123",
            },
          }),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Registration successful",
      content: {
        "application/json": {
          schema: z.object({
            message: z.string().describe("Success message"),
            reportUrl: z.string().describe("Register success"),
          }),
        }
      }
    },
    400: {
      description: "Registration failed",
    },
  },
});

const profileRoute = createRoute({
  method: "get",
  path: "/profile",
  security: [{ bearerAuth: [] }],
  request: {
    headers: z.object({
      Authorization: z.string()
        .regex(/^Bearer [A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*$/) // More precise JWT pattern
        .describe("JWT Bearer token required. Example: 'Bearer eyJhbGciOiJIUzI1Ni...'")
    })
  },
  responses: {
    200: {
      description: "Profile retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string().describe("Success message"),
            data: z.object({
              id: z.string().describe("User ID"),
              firstName: z.string().describe("User's first name"),
              lastName: z.string().describe("User's last name"),
              email: z.string().email().describe("User's email address"),
              phoneNumber: z.string().describe("User's phone number"),
              createdAt: z.string().describe("Account creation date"),
              updatedAt: z.string().describe("Last account update date"),
            }),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized access",
    },
    404: {
      description: "User not found",
    }
  }
});

const refreshTokenRoute = createRoute({
  method: "post",
  path: "/refresh-token",
  request: {
    body: {
      content: {
        "application/json": {
          schema: refreshTokenSchema.openapi("RefreshTokenSchema", {
            example: {
              refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            },
          }),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Token refreshed successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string().describe("Success message"),
            data: z.object({
              accessToken: z.string().describe("New access token"),
              refreshToken: z.string().describe("New refresh token"),
              expiresAt: z.number().describe("Token expiration timestamp"),
            }),
          }),
        }
      }
    },
    401: {
      description: "Invalid or expired refresh token",
    },
  },
});

const googleAuthRoute = createRoute({
  method: "post",
  path: "/google",
  request: {
    body: {
      content: {
        "application/json": {
          schema: googleAuthSchema.openapi("GoogleAuthSchema", {
            example: {
              googleToken: "ya29.a0AfH6SMC..."
            },
          }),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Google authentication successful",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string().describe("Success message"),
            data: z.object({
              accessToken: z.string().describe("JWT access token"),
              refreshToken: z.string().describe("JWT refresh token"),
              user: z.object({
                id: z.string().describe("User ID"),
                firstName: z.string().describe("User's first name"),
                lastName: z.string().describe("User's last name"),
                email: z.string().email().describe("User's email address"),
                phoneNumber: z.string().describe("User's phone number"),
              }),
              expiresAt: z.number().describe("Token expiration timestamp"),
            }),
          }),
        }
      }
    },
    400: {
      description: "Google authentication failed",
    },
  },
});


authRoutes.openapi(registerRoute, authController.register);
authRoutes.openapi(loginRoute, authController.login);
authRoutes.openapi(refreshTokenRoute, authController.refreshToken);
authRoutes.openapi(googleAuthRoute, authController.googleAuth);

authRoutes.use('/profile', authMiddleware);
authRoutes.openapi(profileRoute, authController.profile);

// Logout routes (require authentication)
authRoutes.use('/logout', authMiddleware);
authRoutes.post('/logout', authController.logout);

authRoutes.use('/logout-all', authMiddleware);
authRoutes.post('/logout-all', authController.logoutAll);

// Test email endpoint removed due to type conflicts
// Use the validation script instead: npx ts-node server/scripts/validate-security-fixes.ts

export { authRoutes }