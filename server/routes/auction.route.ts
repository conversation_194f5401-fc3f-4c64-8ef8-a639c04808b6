import { OpenAPIHono, create<PERSON>out<PERSON>, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import auctionController from "../controllers/auction.controller";
import { authMiddleware } from "../middlewares/auth";

const auctionRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// Winner status response schema
const winnerStatusResponseSchema = z.object({
  status: z.boolean(),
  message: z.string(),
  data: z.object({
    product: z.object({
      id: z.string().uuid(),
      itemName: z.string(),
      slug: z.string().nullable(),
      sellType: z.string(),
      auctionStartDate: z.string().nullable(),
      auctionEndDate: z.string().nullable(),
      auctionCompleted: z.boolean(),
      auctionCompletedAt: z.string().nullable(),
      status: z.string(),
      images: z.array(z.object({
        id: z.string().uuid(),
        imageUrl: z.string(),
        isMain: z.boolean()
      }))
    }),
    auctionStatus: z.object({
      hasEnded: z.boolean(),
      isCompleted: z.boolean(),
      timeRemaining: z.number()
    }),
    userStatus: z.object({
      hasParticipated: z.boolean(),
      winnerStatus: z.enum(["not_participated", "participating", "currently_winning", "likely_winner", "winner", "lost"]),
      isWinner: z.boolean(),
      userHighestBid: z.object({
        id: z.string().uuid(),
        amount: z.number(),
        createdAt: z.string()
      }).nullable()
    }),
    currentWinner: z.object({
      id: z.string().uuid(),
      name: z.string(),
      email: z.string(),
      image: z.string().nullable()
    }).nullable(),
    winningBid: z.object({
      id: z.string().uuid(),
      amount: z.number(),
      createdAt: z.string()
    }).nullable(),
    paymentInfo: z.object({
      paymentDeadline: z.string().nullable(),
      hoursRemaining: z.number().nullable(),
      hasExistingOrder: z.boolean(),
      existingOrder: z.object({
        id: z.string().uuid(),
        orderNumber: z.string(),
        status: z.string(),
        paymentStatus: z.string(),
        total: z.number(),
        currency: z.string(),
        createdAt: z.string()
      }).nullable()
    }).nullable()
  })
});

// Public winner info response schema
const publicWinnerResponseSchema = z.object({
  status: z.boolean(),
  message: z.string(),
  data: z.object({
    product: z.object({
      id: z.string().uuid(),
      itemName: z.string(),
      auctionCompleted: z.boolean(),
      auctionCompletedAt: z.string().nullable(),
      auctionEndDate: z.string().nullable()
    }),
    hasWinner: z.boolean(),
    winner: z.object({
      id: z.string().uuid(),
      name: z.string(),
      image: z.string().nullable()
    }).nullable(),
    winningBid: z.object({
      id: z.string().uuid(),
      amount: z.number(),
      createdAt: z.string()
    }).nullable()
  })
});

// Check winner by product route (protected)
const checkWinnerByProductRoute = createRoute({
  method: "get",
  path: "/winner-by-product/{productId}",
  summary: "Check if authenticated user won a specific auction",
  description: "Returns detailed information about whether the authenticated user won a specific auction, including payment status and deadlines",
  request: {
    params: z.object({
      productId: z.string().uuid("Invalid product ID").openapi({
        param: {
          name: 'productId',
          in: 'path',
        },
        example: '6b2c063b-9bd2-4a4c-b87e-3f046b0deb10',
      }),
    }),
  },
  responses: {
    200: {
      description: "Winner status retrieved successfully",
      content: {
        "application/json": {
          schema: winnerStatusResponseSchema,
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized",
    },
    404: {
      description: "Product not found",
    },
  },
});

// Get auction winner route (public)
const getAuctionWinnerRoute = createRoute({
  method: "get",
  path: "/winner/{productId}",
  summary: "Get auction winner information for a product",
  description: "Returns public information about the auction winner (only shows winner info after auction is completed)",
  request: {
    params: z.object({
      productId: z.string().uuid("Invalid product ID").openapi({
        param: {
          name: 'productId',
          in: 'path',
        },
        example: '6b2c063b-9bd2-4a4c-b87e-3f046b0deb10',
      }),
    }),
  },
  responses: {
    200: {
      description: "Auction winner information retrieved successfully",
      content: {
        "application/json": {
          schema: publicWinnerResponseSchema,
        }
      }
    },
    400: {
      description: "Bad request",
    },
    404: {
      description: "Product not found",
    },
  },
});

// Apply auth middleware to protected routes
auctionRoutes.use('/winner-by-product/*', authMiddleware);

// Register routes
auctionRoutes.openapi(checkWinnerByProductRoute, auctionController.checkWinnerByProduct);
auctionRoutes.openapi(getAuctionWinnerRoute, auctionController.getAuctionWinner);

export { auctionRoutes };
