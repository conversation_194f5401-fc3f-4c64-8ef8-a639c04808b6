import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
import { getJakartaTime } from "../utils/timezone.util";

class AuctionService {
  /**
   * Check if a user won a specific auction
   */
  async checkWinnerByProduct(productId: string, userId: string) {
    try {
      // Get the product with winner information
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          winner: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true
            }
          },
          winnerBid: {
            select: {
              id: true,
              amount: true,
              createdAt: true,
              bidderId: true
            }
          },
          images: {
            where: { isMain: true },
            take: 1,
            orderBy: { sortOrder: 'asc' }
          }
        }
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      // Check if this is an auction product
      if (product.sellType !== 'auction') {
        return errorResponse("This product is not an auction item");
      }

      // Check if auction has ended
      const now = getJakartaTime();
      const auctionEndDate = product.auctionEndDate ? new Date(product.auctionEndDate) : null;
      
      if (!auctionEndDate) {
        return errorResponse("Auction end date not set");
      }

      const auctionEnded = now > auctionEndDate;
      const auctionCompleted = product.auctionCompleted;

      // Get user's highest bid for this product
      const userHighestBid = await prisma.bid.findFirst({
        where: {
          productId,
          bidderId: userId
        },
        orderBy: { amount: 'desc' },
        include: {
          bidder: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true
            }
          }
        }
      });

      // Get all bids for this product to determine current highest
      const allBids = await prisma.bid.findMany({
        where: { productId },
        orderBy: { amount: 'desc' },
        take: 1,
        include: {
          bidder: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true
            }
          }
        }
      });

      const currentHighestBid = allBids.length > 0 ? allBids[0] : null;

      // Determine winner status
      let winnerStatus = "not_participated";
      let isWinner = false;
      let paymentDeadline = null;
      let hoursRemaining = null;

      if (userHighestBid) {
        if (auctionCompleted && product.winnerId === userId) {
          // User is confirmed winner (from auction completion process)
          winnerStatus = "winner";
          isWinner = true;
        } else if (auctionEnded && currentHighestBid && currentHighestBid.bidderId === userId) {
          // Auction ended and user has highest bid (but may not be processed yet)
          winnerStatus = "likely_winner";
          isWinner = true;
        } else if (!auctionEnded && currentHighestBid && currentHighestBid.bidderId === userId) {
          // Auction still ongoing and user is currently winning
          winnerStatus = "currently_winning";
          isWinner = false; // Not final winner yet
        } else if (auctionEnded) {
          // User participated but didn't win
          winnerStatus = "lost";
          isWinner = false;
        } else {
          // Auction still ongoing but user is not currently winning
          winnerStatus = "participating";
          isWinner = false;
        }

        // Calculate payment deadline if user won
        if (isWinner && auctionEnded) {
          paymentDeadline = new Date(auctionEndDate.getTime() + (3 * 24 * 60 * 60 * 1000)); // 3 days after auction end
          const timeRemaining = paymentDeadline.getTime() - now.getTime();
          hoursRemaining = Math.max(0, Math.floor(timeRemaining / (1000 * 60 * 60)));
        }
      }

      // Check if user already has an order for this auction
      let existingOrder = null;
      if (isWinner && userHighestBid) {
        existingOrder = await prisma.order.findFirst({
          where: {
            userId,
            OR: [
              // Check if order has bidId directly
              {
                bidId: userHighestBid.id
              },
              // Check if any order item has this bidId and productId
              {
                items: {
                  some: {
                    productId,
                    bidId: userHighestBid.id
                  }
                }
              }
            ]
          },
          include: {
            payment: true,
            items: {
              include: {
                bid: true
              }
            }
          }
        });
      }

      const response = {
        product: {
          id: product.id,
          itemName: product.itemName,
          slug: product.slug,
          sellType: product.sellType,
          auctionStartDate: product.auctionStartDate?.toISOString() || null,
          auctionEndDate: product.auctionEndDate?.toISOString() || null,
          auctionCompleted: product.auctionCompleted,
          auctionCompletedAt: product.auctionCompletedAt?.toISOString() || null,
          status: product.status,
          images: product.images.map(img => ({
            id: img.id,
            imageUrl: img.imageUrl,
            isMain: img.isMain
          }))
        },
        auctionStatus: {
          hasEnded: auctionEnded,
          isCompleted: auctionCompleted,
          timeRemaining: auctionEnded ? 0 : Math.max(0, auctionEndDate.getTime() - now.getTime())
        },
        userStatus: {
          hasParticipated: !!userHighestBid,
          winnerStatus,
          isWinner,
          userHighestBid: userHighestBid ? {
            id: userHighestBid.id,
            amount: Number(userHighestBid.amount),
            createdAt: userHighestBid.createdAt.toISOString()
          } : null
        },
        currentWinner: product.winner ? {
          id: product.winner.id,
          name: `${product.winner.firstName || ''} ${product.winner.lastName || ''}`.trim(),
          email: product.winner.email,
          image: product.winner.image
        } : (currentHighestBid ? {
          id: currentHighestBid.bidder.id,
          name: `${currentHighestBid.bidder.firstName || ''} ${currentHighestBid.bidder.lastName || ''}`.trim(),
          email: currentHighestBid.bidder.email,
          image: currentHighestBid.bidder.image
        } : null),
        winningBid: product.winnerBid ? {
          id: product.winnerBid.id,
          amount: Number(product.winnerBid.amount),
          createdAt: product.winnerBid.createdAt.toISOString()
        } : (currentHighestBid ? {
          id: currentHighestBid.id,
          amount: Number(currentHighestBid.amount),
          createdAt: currentHighestBid.createdAt.toISOString()
        } : null),
        paymentInfo: isWinner ? {
          paymentDeadline: paymentDeadline?.toISOString() || null,
          hoursRemaining,
          hasExistingOrder: !!existingOrder,
          existingOrder: existingOrder ? {
            id: existingOrder.id,
            orderNumber: existingOrder.orderNumber,
            status: existingOrder.status,
            paymentStatus: existingOrder.paymentStatus,
            total: Number(existingOrder.total),
            currency: existingOrder.currency,
            createdAt: existingOrder.createdAt.toISOString()
          } : null
        } : null
      };

      return successResponse("Auction winner status retrieved successfully", response);
    } catch (error) {
      console.error("Check winner by product service error:", error);
      return errorResponse("Failed to check auction winner status");
    }
  }

  /**
   * Get auction winner information for a product (public endpoint)
   */
  async getAuctionWinner(productId: string) {
    try {
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          winner: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              image: true
            }
          },
          winnerBid: {
            select: {
              id: true,
              amount: true,
              createdAt: true
            }
          }
        }
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      if (product.sellType !== 'auction') {
        return errorResponse("This product is not an auction item");
      }

      const now = getJakartaTime();
      const auctionEndDate = product.auctionEndDate ? new Date(product.auctionEndDate) : null;
      const auctionEnded = auctionEndDate ? now > auctionEndDate : false;

      // Only show winner info if auction has ended and been completed
      if (!auctionEnded || !product.auctionCompleted) {
        return successResponse("Auction winner information", {
          product: {
            id: product.id,
            itemName: product.itemName,
            auctionCompleted: product.auctionCompleted,
            auctionEndDate: product.auctionEndDate?.toISOString() || null
          },
          hasWinner: false,
          winner: null,
          winningBid: null
        });
      }

      const response = {
        product: {
          id: product.id,
          itemName: product.itemName,
          auctionCompleted: product.auctionCompleted,
          auctionCompletedAt: product.auctionCompletedAt?.toISOString() || null,
          auctionEndDate: product.auctionEndDate?.toISOString() || null
        },
        hasWinner: !!product.winner,
        winner: product.winner ? {
          id: product.winner.id,
          name: `${product.winner.firstName || ''} ${product.winner.lastName || ''}`.trim(),
          image: product.winner.image
        } : null,
        winningBid: product.winnerBid ? {
          id: product.winnerBid.id,
          amount: Number(product.winnerBid.amount),
          createdAt: product.winnerBid.createdAt.toISOString()
        } : null
      };

      return successResponse("Auction winner information retrieved successfully", response);
    } catch (error) {
      console.error("Get auction winner service error:", error);
      return errorResponse("Failed to get auction winner information");
    }
  }
}

const auctionService = new AuctionService();
export default auctionService;
