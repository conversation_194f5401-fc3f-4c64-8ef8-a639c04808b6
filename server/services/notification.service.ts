import emailService from './email.service';
import webSocketService from './websocket.service';
import { successResponse, errorResponse } from '../utils/response.util';
import { prisma } from '../db';

interface AuctionWinnerNotificationData {
  productId: string;
  productTitle: string;
  productSlug: string;
  productImage: string;
  winnerId: string;
  winnerEmail: string;
  winnerName: string;
  winningBid: number;
  auctionEndDate: string;
  bidId: string;
}

class NotificationService {
  /**
   * Send auction winner notification
   */
  async sendAuctionWinnerNotification(data: AuctionWinnerNotificationData): Promise<any> {
    try {
      console.log(`📧 NotificationService: Processing winner notification for ${data.winnerEmail} - Product: ${data.productTitle}`);

      // Generate checkout URL for the winner
      const checkoutUrl = `${process.env.FRONTEND_URL}/checkout?type=bidding&productId=${data.productId}&bidId=${data.bidId}`;
      console.log(`📧 NotificationService: Generated checkout URL: ${checkoutUrl}`);

      // Send email notification
      console.log(`📧 NotificationService: Calling email service for ${data.winnerEmail}`);
      const emailResult = await emailService.sendAuctionWinnerNotification({
        winnerEmail: data.winnerEmail,
        winnerName: data.winnerName,
        productTitle: data.productTitle,
        productSlug: data.productSlug,
        productImage: data.productImage,
        winningBid: data.winningBid,
        auctionEndDate: data.auctionEndDate,
        checkoutUrl,
        productId: data.productId,
        bidId: data.bidId
      });

      console.log(`📧 NotificationService: Email service result for ${data.winnerEmail}:`, {
        status: emailResult.status,
        message: emailResult.message
      });

      // Send WebSocket notification to the winner
      webSocketService.sendToUser(data.winnerId, {
        type: 'auction_won',
        productId: data.productId,
        productTitle: data.productTitle,
        winningBid: data.winningBid,
        checkoutUrl,
        message: `Congratulations! You won the auction for "${data.productTitle}"`
      });

      // Update bid record with notification status
      if (emailResult.status) {
        await prisma.bid.update({
          where: { id: data.bidId },
          data: { 
            winnerNotified: true,
            winnerNotifiedAt: new Date()
          }
        });

        console.log(`✅ Auction winner notification sent successfully to ${data.winnerEmail}`);
        
        return successResponse('Auction winner notification sent successfully', {
          recipient: data.winnerEmail,
          productTitle: data.productTitle,
          winningBid: data.winningBid,
          emailSent: true,
          websocketSent: true
        });
      } else {
        console.error(`❌ Failed to send email notification to ${data.winnerEmail}:`, emailResult.message);
        
        return errorResponse('Failed to send email notification', emailResult.message || 'Unknown error');
      }

    } catch (error) {
      console.error('❌ Error sending auction winner notification:', error);
      return errorResponse('Failed to send auction winner notification', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Send notification when user is outbid
   */
  async sendOutbidNotification(userId: string, productId: string, previousBid: number, newBid: number): Promise<any> {
    try {
      // Get user and product information
      const [user, product] = await Promise.all([
        prisma.user.findUnique({
          where: { id: userId },
          select: { id: true, firstName: true, lastName: true, email: true }
        }),
        prisma.product.findUnique({
          where: { id: productId },
          select: { id: true, itemName: true, slug: true, images: { where: { isMain: true }, take: 1 } }
        })
      ]);

      if (!user || !product) {
        return errorResponse('User or product not found');
      }

      // Send WebSocket notification
      webSocketService.sendToUser(userId, {
        type: 'outbid',
        productId,
        productTitle: product.itemName,
        previousBid,
        newBid,
        message: `You have been outbid on "${product.itemName}". Current bid: $${newBid}`
      });

      console.log(`📢 Outbid notification sent to user ${user.email} for product: ${product.itemName}`);

      return successResponse('Outbid notification sent', {
        userId,
        productTitle: product.itemName,
        previousBid,
        newBid
      });

    } catch (error) {
      console.error('❌ Error sending outbid notification:', error);
      return errorResponse('Failed to send outbid notification', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Send auction ended notification to all bidders
   */
  async sendAuctionEndedNotifications(productId: string): Promise<any> {
    try {
      // Get product and all bidders
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          bids: {
            include: {
              bidder: {
                select: { id: true, firstName: true, lastName: true, email: true }
              }
            },
            orderBy: { amount: 'desc' }
          },
          images: { where: { isMain: true }, take: 1 }
        }
      });

      if (!product || product.bids.length === 0) {
        return errorResponse('Product not found or no bids');
      }

      const winningBid = product.bids[0];
      const losingBidders = product.bids.slice(1);

      // Notify losing bidders
      for (const bid of losingBidders) {
        webSocketService.sendToUser(bid.bidder.id, {
          type: 'auction_ended',
          productId,
          productTitle: product.itemName,
          userBid: bid.amount,
          winningBid: winningBid.amount,
          won: false,
          message: `Auction ended for "${product.itemName}". Winning bid: $${winningBid.amount}`
        });
      }

      console.log(`📢 Auction ended notifications sent to ${losingBidders.length} losing bidders for: ${product.itemName}`);

      return successResponse('Auction ended notifications sent', {
        productTitle: product.itemName,
        totalBidders: product.bids.length,
        notifiedLosers: losingBidders.length
      });

    } catch (error) {
      console.error('❌ Error sending auction ended notifications:', error);
      return errorResponse('Failed to send auction ended notifications', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Send auction extension notification
   */
  async sendAuctionExtensionNotification(productId: string, extensionData: any): Promise<any> {
    try {
      // Get all users who have bid on this product
      const bidders = await prisma.bid.findMany({
        where: { productId },
        select: { bidderId: true },
        distinct: ['bidderId']
      });

      // Notify all bidders about the extension
      for (const bidder of bidders) {
        webSocketService.sendToUser(bidder.bidderId, {
          type: 'auction_extended',
          productId,
          data: extensionData,
          message: `Auction has been extended by ${extensionData.extendedMinutes} minutes due to recent bidding activity`
        });
      }

      // Also broadcast to anyone viewing the product
      webSocketService.notifyAuctionExtended(productId, extensionData);

      console.log(`📢 Auction extension notifications sent to ${bidders.length} bidders for product: ${productId}`);

      return successResponse('Auction extension notifications sent', {
        productId,
        notifiedBidders: bidders.length,
        extensionMinutes: extensionData.extendedMinutes
      });

    } catch (error) {
      console.error('❌ Error sending auction extension notifications:', error);
      return errorResponse('Failed to send auction extension notifications', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Get notification history for a user
   */
  async getUserNotificationHistory(userId: string, page: number = 1, limit: number = 20): Promise<any> {
    try {
      const skip = (page - 1) * limit;

      // Get user's bid history with notification status
      const [bids, total] = await Promise.all([
        prisma.bid.findMany({
          where: { bidderId: userId },
          include: {
            product: {
              select: {
                id: true,
                itemName: true,
                slug: true,
                auctionEndDate: true,
                auctionCompleted: true,
                images: { where: { isMain: true }, take: 1 }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.bid.count({
          where: { bidderId: userId }
        })
      ]);

      const notifications = bids.map(bid => ({
        id: bid.id,
        type: bid.auctionWon ? 'auction_won' : bid.isWinning ? 'winning_bid' : 'bid_placed',
        productId: bid.product.id,
        productTitle: bid.product.itemName,
        productSlug: bid.product.slug,
        productImage: bid.product.images[0]?.imageUrl || '',
        bidAmount: bid.amount,
        isWinning: bid.isWinning,
        auctionWon: bid.auctionWon,
        winnerNotified: bid.winnerNotified,
        winnerNotifiedAt: bid.winnerNotifiedAt,
        auctionCompleted: bid.product.auctionCompleted,
        auctionEndDate: bid.product.auctionEndDate,
        createdAt: bid.createdAt
      }));

      return successResponse('Notification history retrieved', {
        notifications,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      });

    } catch (error) {
      console.error('❌ Error getting notification history:', error);
      return errorResponse('Failed to get notification history', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Mark notifications as read (for future implementation)
   */
  async markNotificationsAsRead(userId: string, notificationIds: string[]): Promise<any> {
    try {
      // This would be implemented when we add a notifications table
      // For now, we'll just return success
      
      return successResponse('Notifications marked as read', {
        userId,
        markedCount: notificationIds.length
      });

    } catch (error) {
      console.error('❌ Error marking notifications as read:', error);
      return errorResponse('Failed to mark notifications as read', error instanceof Error ? error.message : 'Unknown error');
    }
  }
}

const notificationService = new NotificationService();
export default notificationService;
