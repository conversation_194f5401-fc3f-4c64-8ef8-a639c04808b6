import nodemailer from 'nodemailer';
import { successResponse, errorResponse } from '../utils/response.util';

interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

interface AuctionWinnerEmailData {
  winnerEmail: string;
  winnerName: string;
  productTitle: string;
  productSlug: string;
  productImage: string;
  winningBid: number;
  auctionEndDate: string;
  checkoutUrl: string;
  productId: string;
  bidId: string;
}

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

class EmailService {
  private transporter!: nodemailer.Transporter;
  private isConfigured: boolean = false;

  constructor() {
    this.initializeTransporter();
  }

  /**
   * Get email service configuration status
   */
  get configured(): boolean {
    return this.isConfigured;
  }

  private initializeTransporter() {
    try {
      const smtpPort = parseInt(process.env.SMTP_PORT || '587');
      const emailConfig: EmailConfig = {
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: smtpPort,
        secure: process.env.SMTP_SECURE === 'true' || smtpPort === 465, // Auto-detect secure for port 465
        auth: {
          user: process.env.SMTP_USER || '',
          pass: process.env.SMTP_PASS || '',
        },
      };

      console.log('Initializing email service with config:', {
        host: emailConfig.host,
        port: emailConfig.port,
        secure: emailConfig.secure,
        user: emailConfig.auth.user ? '***configured***' : 'missing',
        pass: emailConfig.auth.pass ? '***configured***' : 'missing'
      });

      if (!emailConfig.auth.user || !emailConfig.auth.pass) {
        console.error('❌ Email service not configured - missing SMTP credentials');
        console.error('Required environment variables: SMTP_USER, SMTP_PASS');
        this.isConfigured = false;
        return;
      }

      // Enhanced transporter configuration
      this.transporter = nodemailer.createTransport({
        ...emailConfig,
        connectionTimeout: 60000, // 60 seconds connection timeout
        greetingTimeout: 30000, // 30 seconds greeting timeout
        socketTimeout: 60000, // 60 seconds socket timeout
        // Gmail-specific options
        requireTLS: true, // Require TLS
        tls: {
          rejectUnauthorized: true, // Reject unauthorized certificates in production
          minVersion: 'TLSv1.2', // Minimum TLS version
        },
        // Additional Gmail settings
        service: 'gmail', // Use Gmail service
        debug: process.env.NODE_ENV === 'development', // Enable debug in development
        logger: process.env.NODE_ENV === 'development', // Enable logging in development
      });

      // Verify connection asynchronously
      this.verifyConnection();

      this.isConfigured = true;

      // Verify connection with timeout and error handling
      this.verifyConnection();
    } catch (error) {
      console.error('Failed to initialize email service:', error);
      this.isConfigured = false;
    }
  }

  /**
   * Verify email connection with proper error handling
   */
  private async verifyConnection() {
    try {
      // Use promise-based verification with timeout
      const verificationPromise = new Promise((resolve, reject) => {
        this.transporter.verify((error, success) => {
          if (error) {
            reject(error);
          } else {
            resolve(success);
          }
        });
      });

      // Add timeout to verification
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Verification timeout')), 10000); // 10 second timeout
      });

      await Promise.race([verificationPromise, timeoutPromise]);
      console.log('✅ Email service configured and verified successfully');
    } catch (error) {
      console.error('Email service verification failed:', error);
      this.isConfigured = false;

      // Don't completely disable the service, just log the warning
      // The service might still work for sending emails even if verification fails
      console.warn('⚠️ Email service verification failed, but service will still attempt to send emails');
      this.isConfigured = true; // Keep service enabled for actual sending
    }
  }



  /**
   * Check if email service is ready and re-verify if needed
   */
  async ensureReady(): Promise<boolean> {
    if (!this.isConfigured) {
      console.log('🔄 Email service not configured, attempting to re-initialize...');
      this.initializeTransporter();

      // Wait a bit for initialization
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    return this.isConfigured;
  }

  /**
   * Generate auction winner email template
   */
  private generateAuctionWinnerTemplate(data: AuctionWinnerEmailData): EmailTemplate {
    const subject = `🎉 Congratulations! You won the auction for ${data.productTitle}`;
    
    const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Auction Winner - ${data.productTitle}</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8f9fa;
            }
            .container {
                background: white;
                border-radius: 12px;
                padding: 30px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #e9ecef;
            }
            .winner-badge {
                background: linear-gradient(135deg, #ffd700, #ffed4e);
                color: #333;
                padding: 12px 24px;
                border-radius: 25px;
                font-weight: bold;
                font-size: 18px;
                display: inline-block;
                margin-bottom: 15px;
                box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
            }
            .product-section {
                display: flex;
                align-items: center;
                margin: 25px 0;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #28a745;
            }
            .product-image {
                width: 120px;
                height: 120px;
                object-fit: cover;
                border-radius: 8px;
                margin-right: 20px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
            .product-details h2 {
                margin: 0 0 10px 0;
                color: #2c3e50;
                font-size: 20px;
            }
            .winning-bid {
                font-size: 24px;
                font-weight: bold;
                color: #28a745;
                margin: 10px 0;
            }
            .auction-info {
                background: #e3f2fd;
                padding: 20px;
                border-radius: 8px;
                margin: 25px 0;
                border-left: 4px solid #2196f3;
            }
            .info-row {
                display: flex;
                justify-content: space-between;
                margin: 8px 0;
                padding: 5px 0;
            }
            .info-label {
                font-weight: 600;
                color: #555;
            }
            .info-value {
                color: #333;
            }
            .cta-section {
                text-align: center;
                margin: 30px 0;
                padding: 25px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 12px;
                color: white;
            }
            .cta-button {
                display: inline-block;
                background: #28a745;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 16px;
                margin: 15px 10px;
                box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
                transition: all 0.3s ease;
            }
            .cta-button:hover {
                background: #218838;
                transform: translateY(-2px);
                box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4);
            }
            .secondary-button {
                background: #6c757d;
                box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
            }
            .secondary-button:hover {
                background: #5a6268;
                box-shadow: 0 6px 12px rgba(108, 117, 125, 0.4);
            }
            .important-note {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
                color: #856404;
            }
            .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e9ecef;
                color: #6c757d;
                font-size: 14px;
            }
            .social-links {
                margin: 15px 0;
            }
            .social-links a {
                color: #6c757d;
                text-decoration: none;
                margin: 0 10px;
            }
            @media (max-width: 600px) {
                .product-section {
                    flex-direction: column;
                    text-align: center;
                }
                .product-image {
                    margin: 0 0 15px 0;
                }
                .cta-button {
                    display: block;
                    margin: 10px 0;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="winner-badge">🏆 AUCTION WINNER! 🏆</div>
                <h1 style="color: #2c3e50; margin: 0;">Congratulations ${data.winnerName}!</h1>
                <p style="color: #6c757d; font-size: 16px; margin: 10px 0 0 0;">
                    You have successfully won the auction
                </p>
            </div>

            <div class="product-section">
                <img src="${data.productImage}" alt="${data.productTitle}" class="product-image" />
                <div class="product-details">
                    <h2>${data.productTitle}</h2>
                    <div class="winning-bid">Your Winning Bid: $${data.winningBid.toLocaleString()}</div>
                    <p style="color: #6c757d; margin: 5px 0;">
                        Auction ended on ${new Date(data.auctionEndDate).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                    </p>
                </div>
            </div>

            <div class="auction-info">
                <h3 style="margin-top: 0; color: #2c3e50;">📋 Auction Summary</h3>
                <div class="info-row">
                    <span class="info-label">Product:</span>
                    <span class="info-value">${data.productTitle}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Your Winning Bid:</span>
                    <span class="info-value" style="font-weight: bold; color: #28a745;">$${data.winningBid.toLocaleString()}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Auction Ended:</span>
                    <span class="info-value">${new Date(data.auctionEndDate).toLocaleDateString()}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Winner:</span>
                    <span class="info-value">${data.winnerName}</span>
                </div>
            </div>

            <div class="important-note">
                <strong>⏰ Important:</strong> Please complete your payment within 48 hours to secure your purchase. 
                After this period, the item may be offered to the next highest bidder.
            </div>

            <div class="cta-section">
                <h3 style="margin-top: 0;">🚀 Complete Your Purchase Now</h3>
                <p style="margin-bottom: 20px; opacity: 0.9;">
                    Click the button below to proceed to checkout and complete your payment securely.
                </p>
                <a href="${data.checkoutUrl}" class="cta-button">
                    💳 Pay Now - $${data.winningBid.toLocaleString()}
                </a>
                <a href="${process.env.FRONTEND_URL}/account/bidding" class="cta-button secondary-button">
                    📊 View My Bids
                </a>
            </div>

            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
                <h4 style="margin-top: 0; color: #2c3e50;">🔄 What Happens Next?</h4>
                <ol style="color: #555; padding-left: 20px;">
                    <li><strong>Complete Payment:</strong> Click the "Pay Now" button above to proceed to secure checkout</li>
                    <li><strong>Order Processing:</strong> Once payment is confirmed, we'll prepare your item for shipping</li>
                    <li><strong>Shipping:</strong> You'll receive tracking information via email when your item ships</li>
                    <li><strong>Track Your Order:</strong> Monitor your order status in your account dashboard</li>
                </ol>
            </div>

            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
                <p style="margin: 0; color: #155724;">
                    <strong>🛡️ Secure Payment:</strong> Your payment is protected by our secure payment system.
                    We accept all major credit cards and PayPal.
                </p>
            </div>

            <div class="footer">
                <p><strong>King Collectibles</strong></p>
                <p>Thank you for choosing our auction platform!</p>
                <div class="social-links">
                    <a href="#">Facebook</a> |
                    <a href="#">Twitter</a> |
                    <a href="#">Instagram</a>
                </div>
                <p style="font-size: 12px; color: #999;">
                    This email was sent to ${data.winnerEmail}. If you have any questions, please contact our support team.
                </p>
            </div>
        </div>
    </body>
    </html>`;

    const text = `
🎉 CONGRATULATIONS! You won the auction for ${data.productTitle}

Dear ${data.winnerName},

Congratulations! You have successfully won the auction for "${data.productTitle}" with your winning bid of $${data.winningBid.toLocaleString()}.

AUCTION DETAILS:
- Product: ${data.productTitle}
- Your Winning Bid: $${data.winningBid.toLocaleString()}
- Auction Ended: ${new Date(data.auctionEndDate).toLocaleDateString()}

NEXT STEPS:
1. Complete your payment within 48 hours: ${data.checkoutUrl}
2. Track your order in your account: ${process.env.FRONTEND_URL}/account/bidding
3. You'll receive shipping confirmation once payment is processed

IMPORTANT: Please complete your payment within 48 hours to secure your purchase.

Complete your purchase now: ${data.checkoutUrl}

Thank you for choosing King Collectibles!

---
King Collectibles Team
Support: <EMAIL>
`;

    return { subject, html, text };
  }

  /**
   * Send auction winner notification email with retry logic
   */
  async sendAuctionWinnerNotification(data: AuctionWinnerEmailData): Promise<any> {
    try {
      // Ensure email service is ready
      const isReady = await this.ensureReady();
      if (!isReady) {
        console.error('❌ Email service not ready, cannot send notification');
        return errorResponse('Email service not configured or connection failed');
      }

      console.log(`📧 Preparing to send auction winner notification to: ${data.winnerEmail}`);

      const template = this.generateAuctionWinnerTemplate(data);

      const mailOptions = {
        from: `"King Collectibles" <${process.env.SMTP_USER}>`,
        to: data.winnerEmail,
        subject: template.subject,
        html: template.html,
        text: template.text,
        attachments: [],
        // Additional options for better delivery
        priority: 'high' as const,
        headers: {
          'X-Mailer': 'King Collectibles Auction System',
          'X-Priority': '1',
        },
      };

      console.log(`📧 Sending email with subject: "${template.subject}"`);

      // Retry logic for email sending
      const maxRetries = 3;
      let lastError;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`📧 Email sending attempt ${attempt}/${maxRetries} to ${data.winnerEmail}`);

          // Add timeout to email sending
          const sendPromise = this.transporter.sendMail(mailOptions);
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Email sending timeout after 60 seconds')), 60000);
          });

          const result = await Promise.race([sendPromise, timeoutPromise]);

          console.log(`✅ Email sent successfully to ${data.winnerEmail} on attempt ${attempt}`);
          console.log(`📧 Message ID: ${result.messageId}`);

          return successResponse('Auction winner notification sent successfully', {
            messageId: result.messageId,
            recipient: data.winnerEmail,
            productTitle: data.productTitle,
            winningBid: data.winningBid,
            attempt: attempt
          });

        } catch (attemptError) {
          lastError = attemptError;
          console.error(`❌ Email sending attempt ${attempt} failed:`, attemptError);

          // Wait before retry (exponential backoff)
          if (attempt < maxRetries) {
            const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
            console.log(`⏳ Waiting ${delay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      // All retries failed
      console.error(`❌ All ${maxRetries} email sending attempts failed for ${data.winnerEmail}`);
      throw lastError;
    } catch (error) {
      console.error('Failed to send auction winner email:', error);

      // Handle specific connection errors
      if (error instanceof Error) {
        if (error.message.includes('Connection closed') || error.message.includes('ECONNRESET')) {
          console.warn('Email connection was closed, attempting to reinitialize...');
          this.initializeTransporter();
        }
      }

      return errorResponse('Failed to send auction winner email', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Send test email (for debugging)
   */
  async sendTestEmail(to: string): Promise<any> {
    try {
      if (!this.isConfigured) {
        return errorResponse('Email service not configured');
      }

      const mailOptions = {
        from: `"King Collectibles" <${process.env.SMTP_USER}>`,
        to,
        subject: 'Test Email from King Collectibles',
        html: '<h1>Test Email</h1><p>This is a test email from King Collectibles auction system.</p>',
        text: 'Test Email - This is a test email from King Collectibles auction system.',
      };

      // Add timeout to email sending
      const sendPromise = this.transporter.sendMail(mailOptions);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Email sending timeout')), 30000); // 30 second timeout
      });

      const result = await Promise.race([sendPromise, timeoutPromise]);

      return successResponse('Test email sent successfully', {
        messageId: result.messageId,
        recipient: to
      });
    } catch (error) {
      console.error('Failed to send test email:', error);

      // Handle specific connection errors
      if (error instanceof Error) {
        if (error.message.includes('Connection closed') || error.message.includes('ECONNRESET')) {
          console.warn('Email connection was closed, attempting to reinitialize...');
          this.initializeTransporter();
        }
      }

      return errorResponse('Failed to send test email', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Check if email service is configured and ready
   */
  isReady(): boolean {
    return this.isConfigured;
  }

  /**
   * Gracefully close email connections
   */
  async close(): Promise<void> {
    try {
      if (this.transporter && this.isConfigured) {
        this.transporter.close();
        console.log('📧 Email service connections closed gracefully');
      }
    } catch (error) {
      console.error('Error closing email service:', error);
    }
  }

  /**
   * Reinitialize the email service (useful for connection recovery)
   */
  reinitialize(): void {
    console.log('🔄 Reinitializing email service...');
    this.isConfigured = false;
    this.initializeTransporter();
  }
}

const emailService = new EmailService();
export default emailService;
