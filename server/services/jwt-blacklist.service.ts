import { prisma } from '../db';

interface BlacklistedToken {
  jti: string;
  userId: string;
  expiresAt: Date;
  reason?: string;
}

class JWTBlacklistService {
  private memoryCache = new Map<string, BlacklistedToken>();
  private cacheExpiry = 60 * 60 * 1000; // 1 hour cache

  constructor() {
    // Clean up expired tokens from memory cache every hour
    setInterval(() => {
      this.cleanupMemoryCache();
    }, this.cacheExpiry);

    // Clean up expired tokens from database daily
    setInterval(() => {
      this.cleanupDatabase();
    }, 24 * 60 * 60 * 1000);
  }

  /**
   * Add token to blacklist
   */
  async blacklistToken(jti: string, userId: string, expiresAt: Date, reason?: string): Promise<void> {
    try {
      const blacklistedToken: BlacklistedToken = {
        jti,
        userId,
        expiresAt,
        reason
      };

      // Add to memory cache for fast lookup
      this.memoryCache.set(jti, blacklistedToken);

      // Add to database for persistence
      await prisma.blacklistedToken.upsert({
        where: { jti },
        update: {
          userId,
          expiresAt,
          reason,
          updatedAt: new Date()
        },
        create: {
          jti,
          userId,
          expiresAt,
          reason,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log(`🚫 Token blacklisted: ${jti} for user ${userId}, reason: ${reason || 'Not specified'}`);
    } catch (error) {
      console.error('Failed to blacklist token:', error);
      throw error;
    }
  }

  /**
   * Check if token is blacklisted
   */
  async isTokenBlacklisted(jti: string): Promise<boolean> {
    try {
      // Check memory cache first
      const cachedToken = this.memoryCache.get(jti);
      if (cachedToken) {
        // Check if token has expired
        if (new Date() > cachedToken.expiresAt) {
          this.memoryCache.delete(jti);
          return false;
        }
        return true;
      }

      // Check database
      const blacklistedToken = await prisma.blacklistedToken.findUnique({
        where: { jti }
      });

      if (blacklistedToken) {
        // Check if token has expired
        if (new Date() > blacklistedToken.expiresAt) {
          // Token has expired, remove from database
          await prisma.blacklistedToken.delete({
            where: { jti }
          });
          return false;
        }

        // Add to memory cache for faster future lookups
        this.memoryCache.set(jti, {
          jti: blacklistedToken.jti,
          userId: blacklistedToken.userId,
          expiresAt: blacklistedToken.expiresAt,
          reason: blacklistedToken.reason || undefined
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error('Failed to check token blacklist:', error);
      // In case of error, assume token is not blacklisted to avoid blocking valid users
      return false;
    }
  }

  /**
   * Blacklist all tokens for a user (useful for logout all devices)
   */
  async blacklistAllUserTokens(userId: string, reason?: string): Promise<void> {
    try {
      // Get all active refresh tokens for the user
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { refreshToken: true }
      });

      if (user?.refreshToken) {
        // For simplicity, we'll clear the refresh token from the user record
        // In a more sophisticated system, you'd track all issued tokens
        await prisma.user.update({
          where: { id: userId },
          data: { refreshToken: null }
        });
      }

      // Remove all cached tokens for this user
      for (const [jti, token] of this.memoryCache.entries()) {
        if (token.userId === userId) {
          this.memoryCache.delete(jti);
        }
      }

      console.log(`🚫 All tokens blacklisted for user ${userId}, reason: ${reason || 'Not specified'}`);
    } catch (error) {
      console.error('Failed to blacklist all user tokens:', error);
      throw error;
    }
  }

  /**
   * Clean up expired tokens from memory cache
   */
  private cleanupMemoryCache(): void {
    const now = new Date();
    let cleanedCount = 0;

    for (const [jti, token] of this.memoryCache.entries()) {
      if (now > token.expiresAt) {
        this.memoryCache.delete(jti);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired tokens from memory cache`);
    }
  }

  /**
   * Clean up expired tokens from database
   */
  private async cleanupDatabase(): Promise<void> {
    try {
      const result = await prisma.blacklistedToken.deleteMany({
        where: {
          expiresAt: {
            lt: new Date()
          }
        }
      });

      if (result.count > 0) {
        console.log(`🧹 Cleaned up ${result.count} expired tokens from database`);
      }
    } catch (error) {
      console.error('Failed to cleanup database tokens:', error);
    }
  }

  /**
   * Get blacklist statistics
   */
  async getStats(): Promise<{ memoryCount: number; databaseCount: number }> {
    try {
      const databaseCount = await prisma.blacklistedToken.count();
      return {
        memoryCount: this.memoryCache.size,
        databaseCount
      };
    } catch (error) {
      console.error('Failed to get blacklist stats:', error);
      return {
        memoryCount: this.memoryCache.size,
        databaseCount: 0
      };
    }
  }
}

export default new JWTBlacklistService();
