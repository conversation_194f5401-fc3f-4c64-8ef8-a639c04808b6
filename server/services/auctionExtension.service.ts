import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
import { getJakartaTime } from "../utils/timezone.util";
import productService from "./product.service";

class AuctionExtensionService {
  /**
   * Get auction extension logs for a specific product
   */
  async getProductExtensionLogs(productId: string) {
    try {
      const extensionLogs = await prisma.auctionExtensionLog.findMany({
        where: { productId },
        orderBy: { createdAt: 'desc' }
      });

      const transformedLogs = extensionLogs.map(log => ({
        id: log.id,
        productId: log.productId,
        previousEndDate: log.previousEndDate.toISOString(),
        newEndDate: log.newEndDate.toISOString(),
        extendedMinutes: log.extendedMinutes,
        triggerBidAmount: Number(log.triggerBidAmount),
        triggeredBy: log.triggeredBy,
        extensionReason: log.extensionReason,
        createdAt: log.createdAt.toISOString()
      }));

      return successResponse("Extension logs retrieved successfully", {
        productId,
        extensionLogs: transformedLogs,
        totalExtensions: transformedLogs.length
      });
    } catch (error) {
      console.error("Get product extension logs error:", error);
      return errorResponse("Failed to retrieve extension logs");
    }
  }

  /**
   * Get all auction extension logs with pagination
   */
  async getAllExtensionLogs(query: {
    page?: number;
    limit?: number;
    productId?: string;
    triggeredBy?: string;
  }) {
    try {
      const page = query.page || 1;
      const limit = query.limit || 20;
      const skip = (page - 1) * limit;

      const where: any = {};
      if (query.productId) {
        where.productId = query.productId;
      }
      if (query.triggeredBy) {
        where.triggeredBy = query.triggeredBy;
      }

      const [extensionLogs, totalCount] = await Promise.all([
        prisma.auctionExtensionLog.findMany({
          where,
          include: {
            product: {
              select: {
                id: true,
                itemName: true,
                slug: true
              }
            },
            triggeredBidder: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.auctionExtensionLog.count({ where })
      ]);

      const transformedLogs = extensionLogs.map(log => ({
        id: log.id,
        productId: log.productId,
        product: log.product,
        previousEndDate: log.previousEndDate.toISOString(),
        newEndDate: log.newEndDate.toISOString(),
        extendedMinutes: log.extendedMinutes,
        triggerBidAmount: Number(log.triggerBidAmount),
        triggeredBy: log.triggeredBy,
        triggeredBidder: log.triggeredBidder,
        extensionReason: log.extensionReason,
        createdAt: log.createdAt.toISOString()
      }));

      const totalPages = Math.ceil(totalCount / limit);

      return successResponse("Extension logs retrieved successfully", {
        extensionLogs: transformedLogs,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          limit,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      });
    } catch (error) {
      console.error("Get all extension logs error:", error);
      return errorResponse("Failed to retrieve extension logs");
    }
  }

  /**
   * Get extension statistics for a product
   */
  async getProductExtensionStats(productId: string) {
    try {
      const stats = await prisma.auctionExtensionLog.aggregate({
        where: { productId },
        _count: {
          id: true
        },
        _sum: {
          extendedMinutes: true
        },
        _avg: {
          triggerBidAmount: true
        },
        _max: {
          triggerBidAmount: true
        }
      });

      const extensionsByType = await prisma.auctionExtensionLog.groupBy({
        by: ['triggeredBy'],
        where: { productId },
        _count: {
          id: true
        }
      });

      return successResponse("Extension statistics retrieved successfully", {
        productId,
        totalExtensions: stats._count.id || 0,
        totalExtendedMinutes: stats._sum.extendedMinutes || 0,
        averageTriggerBid: stats._avg.triggerBidAmount ? Number(stats._avg.triggerBidAmount) : 0,
        highestTriggerBid: stats._max.triggerBidAmount ? Number(stats._max.triggerBidAmount) : 0,
        extensionsByType: extensionsByType.map(item => ({
          triggeredBy: item.triggeredBy,
          count: item._count.id
        }))
      });
    } catch (error) {
      console.error("Get product extension stats error:", error);
      return errorResponse("Failed to retrieve extension statistics");
    }
  }

  // Debug method to check product extend bidding settings
  async debugProductExtendBidding(productId: string) {
    try {
      const product = await prisma.product.findUnique({
        where: { id: productId },
        select: {
          id: true,
          itemName: true,
          sellType: true,
          auctionStartDate: true,
          auctionEndDate: true,
          extendedBiddingEnabled: true,
          extendedBiddingMinutes: true,
          extendedBiddingDuration: true,
          status: true,
          isActive: true,
          auctionCompleted: true
        }
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      // Calculate time remaining
      const now = getJakartaTime();
      const auctionEndDate = new Date(product.auctionEndDate || '');
      const timeRemainingMs = auctionEndDate.getTime() - now.getTime();
      const timeRemainingMinutes = Math.floor(timeRemainingMs / (1000 * 60));

      const debugInfo = {
        product: {
          id: product.id,
          itemName: product.itemName,
          sellType: product.sellType,
          auctionStartDate: product.auctionStartDate,
          auctionEndDate: product.auctionEndDate,
          status: product.status,
          isActive: product.isActive,
          auctionCompleted: product.auctionCompleted
        },
        extendBiddingSettings: {
          extendedBiddingEnabled: product.extendedBiddingEnabled,
          extendedBiddingMinutes: product.extendedBiddingMinutes,
          extendedBiddingDuration: product.extendedBiddingDuration
        },
        timeCalculation: {
          currentTime: now.toISOString(),
          auctionEndTime: auctionEndDate.toISOString(),
          timeRemainingMs,
          timeRemainingMinutes,
          isWithinTriggerWindow: timeRemainingMinutes <= (product.extendedBiddingMinutes || 0) && timeRemainingMinutes > 0
        },
        extendBiddingEligible: {
          isAuction: product.sellType === 'auction',
          isEnabled: product.extendedBiddingEnabled,
          hasMinutes: !!product.extendedBiddingMinutes,
          hasDuration: !!product.extendedBiddingDuration,
          isActive: product.isActive,
          notCompleted: !product.auctionCompleted,
          withinTriggerWindow: timeRemainingMinutes <= (product.extendedBiddingMinutes || 0) && timeRemainingMinutes > 0
        }
      };

      return successResponse("Product extend bidding debug info", debugInfo);
    } catch (error) {
      console.error("Debug product extend bidding error:", error);
      return errorResponse("Failed to get debug info");
    }
  }

  // Test method to simulate extend bidding
  async testExtendBidding(productId: string, bidAmount: number, bidderId?: string) {
    try {
      console.log(`🧪 Testing extend bidding for product ${productId} with bid amount ${bidAmount}`);

      const product = await prisma.product.findUnique({
        where: { id: productId }
      });

      if (!product) {
        console.log(`❌ Product not found: ${productId}`);
        return errorResponse("Product not found");
      }

      console.log(`✅ Product found: ${product.itemName}`);
      console.log(`📋 Product details:`, {
        sellType: product.sellType,
        extendedBiddingEnabled: product.extendedBiddingEnabled,
        extendedBiddingMinutes: product.extendedBiddingMinutes,
        extendedBiddingDuration: product.extendedBiddingDuration,
        auctionEndDate: product.auctionEndDate
      });

      // Call the public method for testing
      console.log(`🔧 Calling handleExtendBidding...`);
      const extensionResult = await productService.handleExtendBidding(product, bidAmount, bidderId);

      console.log(`🎯 Extension result:`, extensionResult);

      return successResponse("Test extend bidding completed", {
        productId,
        bidAmount,
        bidderId,
        extensionResult
      });
    } catch (error) {
      console.error("❌ Test extend bidding error:", error);
      console.error("❌ Error stack:", (error as Error).stack);
      return errorResponse(`Failed to test extend bidding: ${(error as Error).message}`);
    }
  }
}

const auctionExtensionService = new AuctionExtensionService();
export default auctionExtensionService;
