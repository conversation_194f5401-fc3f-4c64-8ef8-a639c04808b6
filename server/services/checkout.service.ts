import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
import shippingService from './shipping.service';
import { getJakartaTime } from "../utils/timezone.util";

class CheckoutService {
  // Generate unique order number
  private generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `ORD-${timestamp}-${random}`;
  }

  /**
   * Enhanced checkout access validation with comprehensive error handling
   * @param userId - User ID
   * @param checkoutType - Type of checkout: 'cart', 'buy-now', 'bidding'
   * @param productId - Product ID (required for buy-now and bidding)
   * @param bidId - Bid ID (optional for bidding)
   */
  async validateCheckoutAccess(userId: string, checkoutType: string, productId?: string, bidId?: string) {
    try {
      console.log(`Validating checkout access: ${checkoutType} for user ${userId}`, { productId, bidId });

      switch (checkoutType) {
        case 'cart':
          return await this.validateCartCheckout(userId);

        case 'buy-now':
          if (!productId) {
            return errorResponse("Product ID is required for buy-now checkout");
          }
          return await this.validateBuyNowCheckout(userId, productId);

        case 'bidding':
          if (!productId) {
            return errorResponse("Product ID is required for bidding checkout");
          }
          return await this.validateBiddingCheckout(userId, productId, bidId);

        default:
          return errorResponse("Invalid checkout type. Must be 'cart', 'buy-now', or 'bidding'");
      }
    } catch (error) {
      console.error("Validate checkout access error:", error);
      return errorResponse("Failed to validate checkout access", {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Enhanced cart checkout validation
   */
  private async validateCartCheckout(userId: string) {
    console.log(`Validating cart checkout for user: ${userId}`);

    const cart = await prisma.cart.findUnique({
      where: { userId },
      include: {
        items: {
          include: {
            product: {
              include: { images: true }
            }
          }
        }
      }
    });

    if (!cart || cart.items.length === 0) {
      return errorResponse("Your cart is empty. Please add items to your cart before proceeding to checkout.", {
        cart: null,
        itemCount: 0
      });
    }

    // Validate all cart items
    const invalidItems: string[] = [];
    const validItems = [];

    for (const item of cart.items) {
      if (item.product.status !== 'active') {
        invalidItems.push(`"${item.product.itemName}" is no longer available`);
      } else if (item.product.sellType !== 'buy-now') {
        invalidItems.push(`"${item.product.itemName}" is only available through auction`);
      } else {
        validItems.push(item);
      }
    }

    if (invalidItems.length > 0) {
      return errorResponse("Some items in your cart are not available for purchase", {
        cart,
        invalidItems,
        validItemCount: validItems.length,
        totalItemCount: cart.items.length
      });
    }

    // Calculate cart totals
    const subtotal = cart.items.reduce((sum, item) => sum + (Number(item.price) * item.quantity), 0);

    return successResponse("Cart checkout validated successfully", {
      cart: {
        ...cart,
        subtotal,
        itemCount: cart.items.length,
        validItems: cart.items.length
      }
    });
  }

  /**
   * Enhanced buy-now checkout validation
   */
  private async validateBuyNowCheckout(userId: string, productId: string) {
    console.log(`Validating buy-now checkout for user: ${userId}, product: ${productId}`);

    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        images: true,
        seller: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    if (!product) {
      return errorResponse("Product not found. The product you're trying to purchase may have been removed.", {
        productId,
        found: false
      });
    }

    if (product.status !== 'active') {
      return errorResponse("This product is no longer available for purchase.", {
        product: {
          id: product.id,
          itemName: product.itemName,
          status: product.status
        }
      });
    }

    if (product.sellType !== 'buy-now') {
      return errorResponse("This product is only available through auction. Please visit the auction page to place a bid.", {
        product: {
          id: product.id,
          itemName: product.itemName,
          sellType: product.sellType,
          auctionEndDate: product.auctionEndDate
        }
      });
    }

    // Check if user is trying to buy their own product
    if (product.sellerId === userId) {
      return errorResponse("You cannot purchase your own product.", {
        product: {
          id: product.id,
          itemName: product.itemName
        }
      });
    }

    return successResponse("Buy-now checkout validated successfully", {
      product: {
        ...product,
        priceUSD: Number(product.priceUSD)
      }
    });
  }

  /**
   * Enhanced bidding checkout validation with comprehensive winner verification
   */
  private async validateBiddingCheckout(userId: string, productId: string, bidId?: string) {
    console.log(`Validating bidding checkout for user: ${userId}, product: ${productId}, bid: ${bidId}`);

    const now = getJakartaTime();

    // Get product details with comprehensive winner information
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        images: true,
        winner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        winnerBid: true,
        bids: {
          orderBy: { amount: 'desc' },
          take: 5, // Get top 5 bids for context
          include: {
            bidder: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          }
        },
        seller: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    if (!product) {
      return errorResponse("Auction product not found. The auction you're trying to access may have been removed.", {
        productId,
        found: false
      });
    }

    if (product.sellType !== 'auction') {
      return errorResponse("This product is not an auction item. Please use the regular checkout for buy-now products.", {
        product: {
          id: product.id,
          itemName: product.itemName,
          sellType: product.sellType
        }
      });
    }

    // Check if auction has ended
    if (!product.auctionEndDate) {
      return errorResponse("This auction has no end date set. Please contact support.", {
        product: {
          id: product.id,
          itemName: product.itemName
        }
      });
    }

    const auctionEndDate = new Date(product.auctionEndDate);
    if (auctionEndDate > now) {
      const timeUntilEnd = auctionEndDate.getTime() - now.getTime();
      const hoursUntilEnd = Math.floor(timeUntilEnd / (1000 * 60 * 60));

      return errorResponse("This auction is still ongoing. You can only checkout after the auction ends.", {
        product: {
          id: product.id,
          itemName: product.itemName,
          auctionEndDate: product.auctionEndDate
        },
        hoursUntilEnd,
        isOngoing: true
      });
    }

    // Check if auction is completed
    if (!product.auctionCompleted) {
      return errorResponse("This auction has ended but is still being processed. Please wait for auction completion and try again in a few minutes.", {
        product: {
          id: product.id,
          itemName: product.itemName,
          auctionEndDate: product.auctionEndDate
        },
        processingStatus: 'pending'
      });
    }

    // Check if there are any bids
    if (!product.bids || product.bids.length === 0) {
      return errorResponse("No bids were placed on this auction.", {
        product: {
          id: product.id,
          itemName: product.itemName
        },
        bidCount: 0
      });
    }

    // Get the winning bid - prioritize winnerBid relation, fallback to highest bid
    const winningBid = product.winnerBid || product.bids[0];
    if (!winningBid) {
      return errorResponse("No winning bid could be determined for this auction.", {
        product: {
          id: product.id,
          itemName: product.itemName
        }
      });
    }

    // Verify user is the winner - check both winnerId and bidder ID
    const isWinnerByProduct = product.winnerId === userId;
    const isWinnerByBid = winningBid.bidderId === userId;

    if (!isWinnerByProduct && !isWinnerByBid) {
      // Find the bidder info from the bids array
      const winningBidWithBidder = product.bids.find(bid => bid.id === winningBid.id);
      const actualWinner = product.winner || winningBidWithBidder?.bidder;

      return errorResponse("You are not the winner of this auction. Only the winning bidder can proceed to checkout.", {
        product: {
          id: product.id,
          itemName: product.itemName
        },
        winningBid: {
          amount: Number(winningBid.amount),
          bidderId: winningBid.bidderId
        },
        actualWinner: actualWinner ? {
          id: actualWinner.id,
          name: `${actualWinner.firstName} ${actualWinner.lastName}`
        } : null,
        userBids: product.bids.filter(bid => bid.bidderId === userId).map(bid => ({
          amount: Number(bid.amount),
          createdAt: bid.createdAt
        }))
      });
    }

    // Calculate payment deadline (3 days after auction end)
    const paymentDeadline = new Date(auctionEndDate.getTime() + (3 * 24 * 60 * 60 * 1000));

    // Check if payment deadline has expired
    if (now > paymentDeadline) {
      const hoursOverdue = Math.floor((now.getTime() - paymentDeadline.getTime()) / (1000 * 60 * 60));
      return errorResponse("Payment deadline has expired. You can no longer checkout for this auction. Please contact support if you need assistance.", {
        product: {
          id: product.id,
          itemName: product.itemName
        },
        paymentDeadline: paymentDeadline.toISOString(),
        auctionEndDate: auctionEndDate.toISOString(),
        hoursOverdue,
        expired: true
      });
    }

    // Check if user already has an order for this auction
    const existingOrder = await prisma.order.findFirst({
      where: {
        userId,
        OR: [
          // Check if order has bidId directly
          { bidId: winningBid.id },
          // Check if any order item has this bidId and productId
          {
            items: {
              some: {
                productId,
                bidId: winningBid.id
              }
            }
          }
        ]
      },
      include: {
        items: true,
        payment: true
      }
    });

    if (existingOrder) {
      return errorResponse("You already have an order for this auction. Please complete the payment for your existing order.", {
        existingOrder: {
          id: existingOrder.id,
          orderNumber: existingOrder.orderNumber,
          status: existingOrder.status,
          paymentStatus: existingOrder.paymentStatus,
          total: Number(existingOrder.total),
          currency: existingOrder.currency,
          createdAt: existingOrder.createdAt
        }
      });
    }

    // Find the bidder info from the bids array for the response
    const winningBidWithBidder = product.bids.find(bid => bid.id === winningBid.id);

    return successResponse("Bidding checkout validated successfully", {
      product: {
        ...product,
        priceUSD: Number(product.priceUSD),
        currentBid: product.currentBid ? Number(product.currentBid) : null
      },
      winningBid: {
        id: winningBid.id,
        amount: Number(winningBid.amount),
        bidderId: winningBid.bidderId,
        bidder: winningBidWithBidder?.bidder || product.winner,
        createdAt: winningBid.createdAt
      },
      paymentDeadline: paymentDeadline.toISOString(),
      auctionEndDate: auctionEndDate.toISOString(),
      hoursRemaining: Math.max(0, Math.floor((paymentDeadline.getTime() - now.getTime()) / (1000 * 60 * 60))),
      isUrgent: (paymentDeadline.getTime() - now.getTime()) < (24 * 60 * 60 * 1000) // Less than 24 hours
    });
  }

  // Create shipping address
  async createShippingAddress(userId: string, addressData: any) {
    try {
      // If this is set as default, unset other default addresses
      if (addressData.isDefault) {
        await prisma.shippingAddress.updateMany({
          where: { userId, isDefault: true },
          data: { isDefault: false }
        });
      }

      const address = await prisma.shippingAddress.create({
        data: {
          ...addressData,
          userId
        }
      });

      return successResponse("Shipping address created successfully", address);
    } catch (error) {
      console.error("Create shipping address service error:", error);
      return errorResponse("Failed to create shipping address");
    }
  }

  // Get user's shipping addresses
  async getShippingAddresses(userId: string) {
    try {
      const addresses = await prisma.shippingAddress.findMany({
        where: { userId },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      return successResponse("Shipping addresses retrieved successfully", addresses);
    } catch (error) {
      console.error("Get shipping addresses service error:", error);
      return errorResponse("Failed to retrieve shipping addresses");
    }
  }

  // Update shipping address
  async updateShippingAddress(userId: string, addressId: string, addressData: any) {
    try {
      // Verify address belongs to user
      const existingAddress = await prisma.shippingAddress.findFirst({
        where: { id: addressId, userId }
      });

      if (!existingAddress) {
        return errorResponse("Shipping address not found");
      }

      // If this is set as default, unset other default addresses
      if (addressData.isDefault) {
        await prisma.shippingAddress.updateMany({
          where: { userId, isDefault: true, id: { not: addressId } },
          data: { isDefault: false }
        });
      }

      const address = await prisma.shippingAddress.update({
        where: { id: addressId },
        data: addressData
      });

      return successResponse("Shipping address updated successfully", address);
    } catch (error) {
      console.error("Update shipping address service error:", error);
      return errorResponse("Failed to update shipping address");
    }
  }

  // Delete shipping address
  async deleteShippingAddress(userId: string, addressId: string) {
    try {
      // Verify address belongs to user
      const existingAddress = await prisma.shippingAddress.findFirst({
        where: { id: addressId, userId }
      });

      if (!existingAddress) {
        return errorResponse("Shipping address not found");
      }

      await prisma.shippingAddress.delete({
        where: { id: addressId }
      });

      return successResponse("Shipping address deleted successfully");
    } catch (error) {
      console.error("Delete shipping address service error:", error);
      return errorResponse("Failed to delete shipping address");
    }
  }

  // Create order from cart
  async createOrderFromCart(userId: string, orderData: any) {
    try {
      // Get user's cart
      const cart = await prisma.cart.findUnique({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              }
            }
          }
        }
      });

      if (!cart || cart.items.length === 0) {
        return errorResponse("Cart is empty");
      }

      // Validate all products are available
      for (const item of cart.items) {
        if (item.product.status !== 'active') {
          return errorResponse(`Product "${item.product.itemName}" is no longer available`);
        }
        if (item.product.sellType !== 'buy-now') {
          return errorResponse(`Product "${item.product.itemName}" is only available through auction`);
        }
      }

      // Handle shipping address
      let shippingAddressId = orderData.shippingAddressId;
      if (!shippingAddressId && orderData.shippingAddress) {
        const addressResult = await this.createShippingAddress(userId, orderData.shippingAddress);
        if (!addressResult.status) {
          return addressResult;
        }
        shippingAddressId = addressResult.data?.id ?? '';
      }

      // Calculate totals
      const subtotal = cart.items.reduce((sum, item) => sum + (Number(item.price) * item.quantity), 0);

      // Calculate dynamic shipping cost
      let shippingCost = 0;
      if (shippingAddressId) {
        const shippingResult = await shippingService.getShippingOptions(shippingAddressId, cart.items);
        if (shippingResult.status && shippingResult.data?.rates?.length > 0) {
          // Use the cheapest shipping option by default
          shippingCost = Math.min(...shippingResult.data.rates.map((rate: any) => rate.cost));
        } else {
          // Fallback to fixed cost if shipping calculation fails
          shippingCost = 15.00;
        }
      } else {
        shippingCost = 15.00; // Default shipping cost
      }

      const tax = subtotal * 0.1; // 10% tax
      const total = subtotal + shippingCost + tax;

      // Create order
      const order = await prisma.order.create({
        data: {
          userId,
          orderNumber: this.generateOrderNumber(),
          status: 'pending',
          paymentStatus: 'pending',
          paymentMethod: orderData.paymentMethod,
          currency: orderData.currency || 'USD',
          subtotal,
          shippingCost,
          tax,
          total,
          shippingAddressId,
          notes: orderData.notes,
          items: {
            create: cart.items.map(item => ({
              productId: item.productId,
              quantity: item.quantity,
              price: item.price,
              currency: orderData.currency || 'USD'
            }))
          }
        },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      // Clear cart after successful order creation
      await prisma.cartItem.deleteMany({
        where: { cartId: cart.id }
      });

      const response = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Order created successfully", response);
    } catch (error) {
      console.error("Create order from cart service error:", error);
      return errorResponse("Failed to create order");
    }
  }

  // Create buy now order (single product)
  async createBuyNowOrder(userId: string, orderData: any) {
    try {
      const { products: productIds, quantity, paymentMethod } = orderData;

      const products = await prisma.product.findMany({
        where: { id: { in: productIds } },
      });

      if (!products || products.length === 0) {
        return errorResponse("Products not found");
      }

      // For single buy now, just use the first product
      const product = products[0];

      if (!product) {
        return errorResponse("Product not found");
      }

      // Check if all products are active
      const inactiveProducts = products.filter(item => item.status !== 'active');
      if (inactiveProducts.length > 0) {
        return errorResponse("Some products are not available for purchase");
      }

      // Check if all products are buy-now type (unless this is a bidding order)
      if (!orderData.orderType || orderData.orderType !== 'bidding') {
        const auctionProducts = products.filter(item => item.sellType !== 'buy-now');
        if (auctionProducts.length > 0) {
          return errorResponse("Some products are only available through auction");
        }
      }

      // Additional validation for bidding orders
      if (orderData.orderType === 'bidding') {
        // Validate that this is an auction product
        if (product.sellType !== 'auction') {
          return errorResponse("This product is not an auction item");
        }

        // Validate auction has ended
        const now = getJakartaTime();
        if (!product.auctionEndDate || new Date(product.auctionEndDate) > now) {
          return errorResponse("Auction is still ongoing or has no end date");
        }

        // Validate auction is completed
        if (!product.auctionCompleted) {
          return errorResponse("Auction has not been processed yet");
        }

        // Get winning bid and validate user is the winner
        const winningBid = await prisma.bid.findFirst({
          where: { productId: product.id },
          orderBy: { amount: 'desc' },
          include: { bidder: true }
        });

        if (!winningBid) {
          return errorResponse("No bids found for this auction");
        }

        if (winningBid.bidderId !== userId) {
          return errorResponse("You are not the winner of this auction");
        }

        // Check payment deadline
        const auctionEndDate = new Date(product.auctionEndDate);
        const paymentDeadline = new Date(auctionEndDate.getTime() + (3 * 24 * 60 * 60 * 1000));

        if (now > paymentDeadline) {
          return errorResponse("Payment deadline has expired");
        }

        // Check if order already exists for this auction
        const existingOrder = await prisma.order.findFirst({
          where: {
            userId,
            items: {
              some: {
                productId: product.id,
                bidId: winningBid.id
              }
            }
          }
        });

        if (existingOrder) {
          return errorResponse("Order already exists for this auction", {
            existingOrder: {
              id: existingOrder.id,
              orderNumber: existingOrder.orderNumber
            }
          });
        }

        // Set the winning bid data for order creation
        orderData.winningBid = Number(winningBid.amount);
        orderData.bidId = winningBid.id;
      }

      // Handle shipping address
      let shippingAddressId = orderData.shippingAddressId;
      if (!shippingAddressId && orderData.shippingAddress) {
        const addressResult = await this.createShippingAddress(userId, orderData.shippingAddress);
        if (!addressResult.status) {
          return addressResult;
        }
        shippingAddressId = addressResult.data?.id ?? '';
      }

      // Calculate subtotal - use winning bid amount for bidding orders
      let subtotal = 0;
      if (orderData.orderType === 'bidding' && orderData.winningBid) {
        // Winning bid is stored in USD, convert to order currency if needed
        const winningBidUSD = Number(orderData.winningBid);
        if (orderData.currency === 'IDR') {
          // Convert USD to IDR
          const currencyService = (await import('./currency.service')).default;
          subtotal = await currencyService.convertCurrency(winningBidUSD, 'USD', 'IDR');
        } else {
          subtotal = winningBidUSD;
        }
      } else {
        // Regular products - convert from USD to order currency if needed
        const productTotalUSD = products.reduce((total, item) => {
          return total + Number(item.priceUSD ?? 0)
        }, 0);

        if (orderData.currency === 'IDR') {
          const currencyService = (await import('./currency.service')).default;
          subtotal = await currencyService.convertCurrency(productTotalUSD, 'USD', 'IDR');
        } else {
          subtotal = productTotalUSD;
        }
      }

      // Calculate dynamic shipping cost for buy now orders
      let shippingCost = 0;
      if (shippingAddressId) {
        const cartItems = productIds.map((id: string) => ({
          product: products.find(p => p.id === id),
          quantity: quantity || 1
        }));

        const shippingResult = await shippingService.getShippingOptions(shippingAddressId, cartItems);
        if (shippingResult.status && shippingResult.data?.rates?.length > 0) {
          // Use the cheapest shipping option by default
          shippingCost = Math.min(...shippingResult.data.rates.map((rate: any) => rate.cost));
        } else {
          // Fallback to fixed cost if shipping calculation fails
          shippingCost = orderData.currency === 'IDR' ? 310000 : 20.00; // 20 USD = ~310,000 IDR
        }
      } else {
        shippingCost = orderData.currency === 'IDR' ? 310000 : 20.00; // Default shipping cost
      }

      const tax = subtotal * 0.1;
      const total = subtotal + shippingCost + tax;

      // Create order
      const order = await prisma.order.create({
        data: {
          userId,
          orderNumber: this.generateOrderNumber(),
          status: 'pending_payment',
          paymentStatus: 'pending',
          paymentMethod: paymentMethod.eWalletType ?? paymentMethod.type,
          currency: orderData.currency || 'USD',
          subtotal,
          shippingCost,
          tax,
          total,
          shippingAddressId,
          notes: orderData.notes,
          // Add bidId for bidding orders
          ...(orderData.orderType === 'bidding' && orderData.bidId && {
            bidId: orderData.bidId
          }),
          items: {
            create: productIds.map((id: string) => {
              const product = products.find(p => p.id === id);
              let itemPrice = Number(product?.priceUSD ?? 0);

              // For bidding orders, use the converted subtotal as the item price
              if (orderData.orderType === 'bidding' && orderData.winningBid) {
                itemPrice = subtotal; // This is already converted to the correct currency
              } else if (orderData.currency === 'IDR') {
                // Convert regular product price to IDR if needed
                itemPrice = subtotal / productIds.length; // Distribute total among items
              }

              return {
                productId: id,
                quantity: quantity || 1,
                price: itemPrice,
                currency: orderData.currency || 'USD',
                // Add bidding information if applicable
                ...(orderData.orderType === 'bidding' && orderData.bidId && {
                  bidId: orderData.bidId
                })
              };
            })
          }
        },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      // Get user data for payment
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        return errorResponse("User not found");
      }

      // Create payment record and Xendit payment
      const paymentService = (await import('./payment.service')).default;
      let paymentResult = null;

      console.log("Creating payment for order:", order.id, "with method:", orderData.paymentMethod.type);

      // Create payment based on payment method
      if (orderData.paymentMethod.type === 'xendit_invoice' || orderData.paymentMethod.type === 'invoice') {
        console.log("Creating Xendit invoice for order:", order.id);
        paymentResult = await paymentService.createInvoice({
          orderId: order.id,
          currency: orderData.currency || 'USD',
          description: `Payment for Order ${order.orderNumber}`,
          customerEmail: user.email || '<EMAIL>',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'ewallet') {
        console.log("Creating eWallet payment for order:", order.id, "with type:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createEWalletCharge({
          orderId: order.id,
          currency: orderData.currency || 'USD',
          ewalletType: orderData.paymentMethod.ewalletType || 'OVO',
          customerPhone: user.phoneNumber || '************',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'virtual_account') {
        console.log("Creating virtual account payment for order:", order.id, "with bank code:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createVirtualAccount({
          orderId: order.id,
          currency: orderData.currency || 'IDR',
          bankCode: orderData.paymentMethod.ewalletType || 'BCA',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'retail_outlet') {
        console.log("Creating retail outlet payment for order:", order.id, "with outlet name:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createRetailOutlet({
          orderId: order.id,
          currency: orderData.currency || 'IDR',
          retailOutletName: orderData.paymentMethod.ewalletType || 'INDOMARET',
          customerName: `${user.firstName} ${user.lastName}`
        });
      } else if (orderData.paymentMethod.type === 'qr_code') {
        console.log("Creating QR code payment for order:", order.id, "with QR code type:", orderData.paymentMethod.channel);
        paymentResult = await paymentService.createQRCode({
          orderId: order.id,
          currency: orderData.currency || 'IDR',
          qrCodeType: orderData.paymentMethod.ewalletType || 'QRIS',
          customerName: `${user.firstName} ${user.lastName}`
        });
      }

      // Clear cart if this is a buy now order
      await prisma.cartItem.deleteMany({
        where: { cart: { userId } }
      });

      // Get updated order with payment
      const updatedOrder = await prisma.order.findUnique({
        where: { id: order.id },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true,
          payment: true
        }
      });

      const response = {
        ...updatedOrder,
        subtotal: Number(updatedOrder!.subtotal),
        shippingCost: Number(updatedOrder!.shippingCost),
        tax: Number(updatedOrder!.tax),
        total: Number(updatedOrder!.total),
        items: updatedOrder!.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: products.map(prod => ({
            ...prod,
            priceUSD: Number(prod.priceUSD),
            currentBid: prod.currentBid ? Number(prod.currentBid) : null,
          }))
        })),
        paymentUrl: (paymentResult?.data as any)?.invoiceUrl || (paymentResult?.data as any)?.actions?.mobile_deeplink_checkout_url
      };

      return successResponse("Order created successfully", response);
    } catch (error) {
      console.error("Create buy now order service error:", error);
      return errorResponse("Failed to create order");
    }
  }

  // Get user's orders
  async getOrders(userId: string, query: any) {
    try {
      const { page, limit, status, paymentStatus, sortBy, sortOrder } = query;
      const skip = (page - 1) * limit;

      const where: any = { userId };
      if (status) where.status = status;
      if (paymentStatus) where.paymentStatus = paymentStatus;

      const orders = await prisma.order.findMany({
        where,
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit
      });

      const total = await prisma.order.count({ where });

      const response = {
        orders: orders.map(order => ({
          ...order,
          subtotal: Number(order.subtotal),
          shippingCost: Number(order.shippingCost),
          tax: Number(order.tax),
          total: Number(order.total),
          items: order.items.map(item => ({
            ...item,
            price: Number(item.price),
            product: {
              ...item.product,
              priceUSD: Number(item.product.priceUSD),
              currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
            }
          }))
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

      return successResponse("Orders retrieved successfully", response);
    } catch (error) {
      console.error("Get orders service error:", error);
      return errorResponse("Failed to retrieve orders");
    }
  }

  // Get single order
  async getOrder(userId: string, orderId: string) {
    try {
      const order = await prisma.order.findFirst({
        where: { id: orderId, userId },
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      const response = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Order retrieved successfully", response);
    } catch (error) {
      console.error("Get order service error:", error);
      return errorResponse("Failed to retrieve order");
    }
  }

  // Update order status (admin function)
  async updateOrderStatus(orderId: string, statusData: any) {
    try {
      const order = await prisma.order.update({
        where: { id: orderId },
        data: statusData,
        include: {
          items: {
            include: {
              product: {
                include: { images: true }
              },
              bid: true // Include bid information for auction orders
            }
          },
          shippingAddress: true
        }
      });

      const response = {
        ...order,
        subtotal: Number(order.subtotal),
        shippingCost: Number(order.shippingCost),
        tax: Number(order.tax),
        total: Number(order.total),
        items: order.items.map(item => ({
          ...item,
          price: Number(item.price),
          product: {
            ...item.product,
            priceUSD: Number(item.product.priceUSD),
            currentBid: item.product.currentBid ? Number(item.product.currentBid) : null,
          }
        }))
      };

      return successResponse("Order status updated successfully", response);
    } catch (error) {
      console.error("Update order status service error:", error);
      return errorResponse("Failed to update order status");
    }
  }
}

const checkoutService = new CheckoutService();
export default checkoutService;
