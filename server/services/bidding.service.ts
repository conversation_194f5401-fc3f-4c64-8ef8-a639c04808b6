import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
import { UserBidsQuery } from "../schemas/bidding.schema";
import { getJakartaTime } from "../utils/timezone.util";

class BiddingService {
  // Get user's bidding history with pagination and filtering
  async getUserBids(userId: string, query: UserBidsQuery) {
    try {
      const { page, limit, status, sortBy, sortOrder } = query;
      const skip = (page - 1) * limit;

      // Build where clause for bids
      const where: any = { bidderId: userId };

      // Get user's bids grouped by product
      const userBids = await prisma.bid.findMany({
        where,
        include: {
          product: {
            include: {
              images: {
                where: { isMain: true },
                orderBy: { sortOrder: 'asc' }
              },
              bids: {
                orderBy: { amount: 'desc' },
                take: 1,
                include: {
                  bidder: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      // Group bids by product and get summary
      const bidSummaryMap = new Map();
      
      for (const bid of userBids) {
        const productId = bid.productId;
        
        if (!bidSummaryMap.has(productId)) {
          // Get all bids for this product by this user
          const userProductBids = await prisma.bid.findMany({
            where: {
              productId,
              bidderId: userId
            },
            orderBy: { amount: 'desc' }
          });

          // Get highest bid for this product (from any user)
          const highestBid = await prisma.bid.findFirst({
            where: { productId },
            orderBy: { amount: 'desc' },
            include: { bidder: true }
          });

          // Determine auction status
          const auctionStatus = this.calculateAuctionStatusForBid(bid.product, userId, highestBid);

          // Filter by status if provided
          if (status && auctionStatus !== status) {
            continue;
          }

          const userHighestBid = userProductBids[0];
          const isWinning = highestBid?.bidderId === userId;

          // Get order information for this product/bid if user is winning
          let orderInfo = null;
          if (isWinning && auctionStatus === 'won') {
            const existingOrder = await prisma.order.findFirst({
              where: {
                userId,
                OR: [
                  // Check if order has bidId directly
                  {
                    bidId: userHighestBid.id
                  },
                  // Check if any order item has this bidId and productId
                  {
                    items: {
                      some: {
                        productId,
                        bidId: userHighestBid.id
                      }
                    }
                  }
                ]
              },
              include: {
                payment: true,
                items: {
                  include: {
                    bid: true
                  }
                }
              }
            });

            if (existingOrder) {
              orderInfo = {
                id: existingOrder.id,
                orderNumber: existingOrder.orderNumber,
                status: existingOrder.status,
                paymentStatus: existingOrder.paymentStatus,
                total: Number(existingOrder.total),
                currency: existingOrder.currency,
                createdAt: existingOrder.createdAt.toISOString(),
                payment: existingOrder.payment ? {
                  id: existingOrder.payment.id,
                  status: existingOrder.payment.status,
                  invoiceUrl: existingOrder.payment.invoiceUrl,
                  amount: Number(existingOrder.payment.amount),
                  currency: existingOrder.payment.currency
                } : null
              };
            }
          }

          bidSummaryMap.set(productId, {
            productId,
            product: {
              id: bid.product.id,
              itemName: bid.product.itemName,
              slug: bid.product.slug,
              currentBid: bid.product.currentBid ? Number(bid.product.currentBid) : null,
              auctionEndDate: bid.product.auctionEndDate?.toISOString() || null,
              status: bid.product.status,
              images: bid.product.images.map(img => ({
                id: img.id,
                imageUrl: img.imageUrl,
                isMain: img.isMain,
              })),
            },
            highestBid: Number(userHighestBid.amount),
            totalBids: userProductBids.length,
            isWinning,
            lastBidTime: userHighestBid.createdAt.toISOString(),
            auctionStatus,
            order: orderInfo, // Include order information
            paymentStatus: this.determinePaymentStatus(auctionStatus, orderInfo), // Add payment status helper
          });
        }
      }

      // Convert map to array and apply sorting
      const bidSummaries = Array.from(bidSummaryMap.values());

      // Sort results
      bidSummaries.sort((a, b) => {
        let aValue, bValue;
        
        switch (sortBy) {
          case 'amount':
            aValue = a.highestBid;
            bValue = b.highestBid;
            break;
          case 'auctionEndDate':
            aValue = new Date(a.product.auctionEndDate || 0).getTime();
            bValue = new Date(b.product.auctionEndDate || 0).getTime();
            break;
          default: // createdAt
            aValue = new Date(a.lastBidTime).getTime();
            bValue = new Date(b.lastBidTime).getTime();
        }

        if (sortOrder === 'desc') {
          return bValue - aValue;
        }
        return aValue - bValue;
      });

      // Apply pagination
      const total = bidSummaries.length;
      const paginatedBids = bidSummaries.slice(skip, skip + limit);
      const totalPages = Math.ceil(total / limit);

      return successResponse("User bids retrieved successfully", {
        bids: paginatedBids,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        }
      });
    } catch (error) {
      console.error("Get user bids error:", error);
      return errorResponse("Failed to retrieve user bids");
    }
  }

  // Get single bid detail
  async getBidDetail(userId: string, bidId: string) {
    try {
      const bid = await prisma.bid.findFirst({
        where: {
          id: bidId,
          bidderId: userId, // Ensure user can only access their own bids
        },
        include: {
          product: {
            include: {
              images: {
                orderBy: { sortOrder: 'asc' }
              },
              seller: {
                select: {
                  id: true,
                  email: true,
                }
              },
              bids: {
                orderBy: { amount: 'desc' },
                include: {
                  bidder: {
                    select: {
                      id: true,
                      email: true,
                    }
                  }
                }
              }
            }
          },
          bidder: {
            select: {
              id: true,
              email: true,
            }
          },
          orderItems: {
            include: {
              order: {
                include: {
                  payment: true // Include payment information for invoice URL
                }
              }
            }
          }
        }
      });

      if (!bid) {
        return errorResponse("Bid not found");
      }

      // Transform data to match schema
      const transformedBid = {
        ...bid,
        amount: Number(bid.amount),
        createdAt: bid.createdAt.toISOString(),
        product: {
          ...bid.product,
          priceUSD: Number(bid.product.priceUSD),
          currentBid: bid.product.currentBid ? Number(bid.product.currentBid) : null,
          auctionStartDate: bid.product.auctionStartDate?.toISOString() || null,
          auctionEndDate: bid.product.auctionEndDate?.toISOString() || null,
          images: bid.product.images.map(img => ({
            ...img,
            altText: img.altText || null,
          }))
        },
        order: bid.orderItems && bid.orderItems.length > 0 ? {
          id: bid.orderItems[0].order.id,
          orderNumber: bid.orderItems[0].order.orderNumber,
          status: bid.orderItems[0].order.status,
          paymentStatus: bid.orderItems[0].order.paymentStatus,
          total: Number(bid.orderItems[0].order.total),
          currency: bid.orderItems[0].order.currency,
          createdAt: bid.orderItems[0].order.createdAt.toISOString(),
          payment: bid.orderItems[0].order.payment ? {
            id: bid.orderItems[0].order.payment.id,
            status: bid.orderItems[0].order.payment.status,
            invoiceUrl: bid.orderItems[0].order.payment.invoiceUrl,
            amount: Number(bid.orderItems[0].order.payment.amount),
            currency: bid.orderItems[0].order.payment.currency,
            expiryDate: bid.orderItems[0].order.payment.expiryDate?.toISOString() || null
          } : null
        } : null
      };

      return successResponse("Bid detail retrieved successfully", transformedBid);
    } catch (error) {
      console.error("Get bid detail error:", error);
      return errorResponse("Failed to retrieve bid detail");
    }
  }

  // Get bid history for a product
  async getBidHistory(productId: string) {
    try {
      const product = await prisma.product.findUnique({
        where: { id: productId }
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      const bids = await prisma.bid.findMany({
        where: { productId },
        include: {
          bidder: {
            select: {
              id: true,
              email: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      const highestBid = bids.length > 0 ? Math.max(...bids.map(b => Number(b.amount))) : null;
      const currentWinner = bids.find(b => Number(b.amount) === highestBid)?.bidder || null;

      const transformedBids = bids.map(bid => ({
        ...bid,
        amount: Number(bid.amount),
        createdAt: bid.createdAt.toISOString(),
      }));

      return successResponse("Bid history retrieved successfully", {
        productId,
        bids: transformedBids,
        totalBids: bids.length,
        highestBid,
        currentWinner,
      });
    } catch (error) {
      console.error("Get bid history error:", error);
      return errorResponse("Failed to retrieve bid history");
    }
  }

  /**
   * Calculate auction status for a bid considering user context
   */
  private calculateAuctionStatusForBid(product: any, userId: string, highestBid: any): "upcoming" | "active" | "ended" | "won" | "lost" {
    // Get current time in Jakarta timezone
    const now = getJakartaTime();
    const auctionStartDate = product.auctionStartDate;
    const auctionEndDate = product.auctionEndDate;

    if (!auctionStartDate || !auctionEndDate) {
      return "active"; // Default to active if dates not set
    }

    const startDate = new Date(auctionStartDate);
    const endDate = new Date(auctionEndDate);

    if (now < startDate) {
      return "upcoming";
    } else if (now > endDate) {
      // Auction has ended - determine if user won or lost
      if (highestBid && highestBid.bidderId === userId) {
        return "won";
      } else {
        return "lost";
      }
    } else {
      return "active";
    }
  }

  /**
   * Determine payment status for a bid based on auction status and order information
   */
  private determinePaymentStatus(auctionStatus: string, orderInfo: any): "not_required" | "pending" | "paid" | "expired" | "failed" {
    // If auction is not won, payment is not required
    if (auctionStatus !== "won") {
      return "not_required";
    }

    // If no order exists, payment is pending
    if (!orderInfo) {
      return "pending";
    }

    // Check payment status from order
    if (orderInfo.paymentStatus === "paid" || orderInfo.paymentStatus === "completed") {
      return "paid";
    } else if (orderInfo.paymentStatus === "failed") {
      return "failed";
    } else if (orderInfo.paymentStatus === "expired") {
      return "expired";
    } else {
      return "pending";
    }
  }
}

const biddingService = new BiddingService();
export default biddingService;
