#!/bin/bash

# King Collectibles WebSocket Deployment Script
# This script deploys the application with WebSocket support

set -e

echo "🚀 King Collectibles WebSocket Deployment"
echo "=========================================="

# Configuration
DOCKER_IMAGE="reihanpraja/king-collectible"
COMPOSE_FILE="docker-compose.yml"
PRODUCTION_COMPOSE_FILE="docker-compose.production.yml"

# Parse command line arguments
ENVIRONMENT="development"
BUILD_IMAGE=false
PULL_IMAGE=false
USE_SSL=false

while [[ $# -gt 0 ]]; do
  case $1 in
    --env)
      ENVIRONMENT="$2"
      shift 2
      ;;
    --build)
      BUILD_IMAGE=true
      shift
      ;;
    --pull)
      PULL_IMAGE=true
      shift
      ;;
    --ssl)
      USE_SSL=true
      shift
      ;;
    --help)
      echo "Usage: $0 [OPTIONS]"
      echo ""
      echo "Options:"
      echo "  --env ENV        Set environment (development|production) [default: development]"
      echo "  --build          Build Docker image locally"
      echo "  --pull           Pull latest Docker image from registry"
      echo "  --ssl            Use SSL configuration (production only)"
      echo "  --help           Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

echo "📋 Configuration:"
echo "   Environment: $ENVIRONMENT"
echo "   Build image: $BUILD_IMAGE"
echo "   Pull image: $PULL_IMAGE"
echo "   Use SSL: $USE_SSL"
echo ""

# Validate environment
if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
  echo "❌ Error: Environment must be 'development' or 'production'"
  exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo "❌ Error: Docker is not running"
  exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
  echo "❌ Error: docker-compose is not installed"
  exit 1
fi

# Set compose file based on environment
if [[ "$ENVIRONMENT" == "production" ]]; then
  COMPOSE_FILE="$PRODUCTION_COMPOSE_FILE"
  
  if [[ "$USE_SSL" == true ]]; then
    echo "🔒 SSL mode enabled"
    
    # Check if SSL certificates exist
    if [[ ! -f "ssl/cert.pem" || ! -f "ssl/key.pem" ]]; then
      echo "⚠️  Warning: SSL certificates not found in ssl/ directory"
      echo "   Please ensure you have:"
      echo "   - ssl/cert.pem"
      echo "   - ssl/key.pem"
      echo ""
      read -p "Continue without SSL? (y/N): " -n 1 -r
      echo
      if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
      fi
      USE_SSL=false
    fi
  fi
fi

echo "📁 Using compose file: $COMPOSE_FILE"

# Build or pull image
if [[ "$BUILD_IMAGE" == true ]]; then
  echo "🔨 Building Docker image..."
  docker build -t "$DOCKER_IMAGE:latest" .
elif [[ "$PULL_IMAGE" == true ]]; then
  echo "📥 Pulling Docker image..."
  docker pull "$DOCKER_IMAGE:latest"
fi

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker-compose -f "$COMPOSE_FILE" down --remove-orphans

# Start services
echo "🚀 Starting services..."
if [[ "$USE_SSL" == true ]]; then
  docker-compose -f "$COMPOSE_FILE" up -d
else
  # Start without nginx if no SSL
  docker-compose -f "$COMPOSE_FILE" up -d app websocket db
fi

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

# Check main app
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
  echo "✅ Main application is healthy"
else
  echo "❌ Main application health check failed"
fi

# Check WebSocket
if command -v wscat > /dev/null 2>&1; then
  if timeout 5 wscat -c ws://localhost:3001 --execute "ping" > /dev/null 2>&1; then
    echo "✅ WebSocket server is healthy"
  else
    echo "❌ WebSocket server health check failed"
  fi
else
  echo "⚠️  wscat not available, skipping WebSocket health check"
fi

# Show running containers
echo ""
echo "📊 Running containers:"
docker-compose -f "$COMPOSE_FILE" ps

echo ""
echo "🎉 Deployment complete!"
echo ""
echo "📡 Services:"
echo "   Main App: http://localhost:3000"
echo "   WebSocket: ws://localhost:3001"
if [[ "$USE_SSL" == true ]]; then
  echo "   HTTPS: https://localhost"
  echo "   WSS: wss://localhost:3001"
fi
echo ""
echo "📝 Logs:"
echo "   docker-compose -f $COMPOSE_FILE logs -f"
echo ""
echo "🛑 Stop:"
echo "   docker-compose -f $COMPOSE_FILE down"
