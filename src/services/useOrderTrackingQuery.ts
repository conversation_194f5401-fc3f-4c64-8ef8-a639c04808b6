import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi, useStrictAuthenticatedApi } from "./useAuthQuery";
import { toaster } from "@/components/ui/toaster";
import { SupportedCurrency } from "@/contexts/CurrencyLanguageContext";

// Types
export interface OrderItem {
  id: string;
  productId: string;
  quantity: number;
  price: number;
  currency: string;
  bidId?: string;
  product: {
    id: string;
    itemName: string;
    slug?: string;
    images: Array<{
      id: string;
      imageUrl: string;
      isMain: boolean;
    }>;
  };
}

export interface ShippingAddress {
  id: string;
  firstName: string;
  lastName: string;
  name?: string;
  address: string;
  city: string;
  state: string;
  provinceRegion?: string;
  zipCode: string;
  country: string;
  phoneNumber?: string;
}

export interface OrderStatusHistory {
  id: string;
  status: string;
  description: string;
  createdAt: string;
  createdBy: string;
}

export interface Payment {
  id: string;
  status: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  invoiceUrl?: string;
  paidAt?: string;
  expiryDate?: string;
  expiredAt?: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  status: 'pending_payment' | 'paid' | 'processing' | 'seller_confirmed' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: string;
  currency: SupportedCurrency;
  subtotal: number;
  shippingCost: number;
  tax: number;
  discount?: number;
  total: number;
  trackingNumber?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  items: OrderItem[];
  shippingAddress: ShippingAddress;
  statusHistory: OrderStatusHistory[];
  payment?: Payment;
  progress?: number;
  timeline?: Array<{
    status: string;
    description: string;
    date: string;
    isCompleted: boolean;
    isCurrent: boolean;
  }>;
  estimatedDelivery?: string;
  courier?: string;
  serviceType?: string;
  deliveryInstructions?: string;
}

export interface OrdersListResponse {
  orders: Order[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface OrdersQueryParams {
  page?: number;
  limit?: number;
  status?: string;
  paymentStatus?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'total';
  sortOrder?: 'asc' | 'desc';
}

export interface CreateOrderData {
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
    bidId?: string;
  }>;
  shippingAddressId: string;
  paymentMethod: string;
  currency?: string;
  subtotal: number;
  shippingCost: number;
  tax: number;
  total: number;
  notes?: string;
  orderType?: 'buy-now' | 'bidding';
  winningBid?: number;
  bidId?: string;
}

// Query Keys
export const orderTrackingQueryKeys = {
  all: ['order-tracking'] as const,
  orders: () => [...orderTrackingQueryKeys.all, 'orders'] as const,
  ordersList: (params: OrdersQueryParams) => [...orderTrackingQueryKeys.orders(), params] as const,
  order: (orderId: string) => [...orderTrackingQueryKeys.all, 'order', orderId] as const,
  userOrders: () => [...orderTrackingQueryKeys.all, 'user-orders'] as const,
};

// Custom hook for fetching user orders
export const useUserOrdersQuery = (params: OrdersQueryParams = {}) => {
  const api = useAuthenticatedApi();

  return useQuery({
    queryKey: orderTrackingQueryKeys.ordersList(params),
    queryFn: async (): Promise<OrdersListResponse> => {
      const searchParams = new URLSearchParams();
      
      if (params.page) searchParams.append('page', params.page.toString());
      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.status) searchParams.append('status', params.status);
      if (params.paymentStatus) searchParams.append('paymentStatus', params.paymentStatus);
      if (params.sortBy) searchParams.append('sortBy', params.sortBy);
      if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

      const response = await api.get(`/order-tracking/user?${searchParams.toString()}`);
      return response.data.data;
    },
    enabled: !!api,
  });
};

// Custom hook for fetching single order
export const useOrderQuery = (orderId: string) => {
  const api = useStrictAuthenticatedApi();

  return useQuery({
    queryKey: orderTrackingQueryKeys.order(orderId),
    queryFn: async (): Promise<Order> => {
      if (!api) {
        throw new Error('Authentication required to access order details');
      }
      const response = await api.get(`/order-tracking/tracking/${orderId}`);
      return response.data?.order;
    },
    enabled: !!api && !!orderId,
  });
};

// Custom hook for creating order (for auction winners)
export const useCreateOrderMutation = () => {
  const api = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (orderData: CreateOrderData): Promise<Order> => {
      const response = await api.post('/order-tracking/create', orderData);
      return response.data.data;
    },
    onSuccess: (data) => {
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: orderTrackingQueryKeys.orders() });
      
      toaster.create({
        title: "Order Created Successfully",
        description: `Order #${data.orderNumber} has been created`,
        type: "success",
        duration: 3000,
      });
    },
    onError: (error: any) => {
      toaster.create({
        title: "Failed to Create Order",
        description: error.response?.data?.message || "An error occurred while creating the order",
        type: "error",
        duration: 5000,
      });
    },
  });
};

// Custom hook for updating order status
export const useUpdateOrderStatusMutation = () => {
  const api = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ orderId, status, description }: { 
      orderId: string; 
      status: string; 
      description?: string 
    }): Promise<Order> => {
      const response = await api.put(`/order-tracking/${orderId}/status`, { status, description });
      return response.data.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch specific order and orders list
      queryClient.invalidateQueries({ queryKey: orderTrackingQueryKeys.order(variables.orderId) });
      queryClient.invalidateQueries({ queryKey: orderTrackingQueryKeys.orders() });
      
      toaster.create({
        title: "Order Status Updated",
        description: `Order #${data.orderNumber} status has been updated`,
        type: "success",
        duration: 3000,
      });
    },
    onError: (error: any) => {
      toaster.create({
        title: "Failed to Update Order",
        description: error.response?.data?.message || "An error occurred while updating the order",
        type: "error",
        duration: 5000,
      });
    },
  });
};

// Custom hook for cancelling order
export const useCancelOrderMutation = () => {
  const api = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (orderId: string): Promise<Order> => {
      const response = await api.post(`/order-tracking/${orderId}/cancel`);
      return response.data.data;
    },
    onSuccess: (data) => {
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: orderTrackingQueryKeys.order(data.id) });
      queryClient.invalidateQueries({ queryKey: orderTrackingQueryKeys.orders() });
      
      toaster.create({
        title: "Order Cancelled",
        description: `Order #${data.orderNumber} has been cancelled`,
        type: "info",
        duration: 3000,
      });
    },
    onError: (error: any) => {
      toaster.create({
        title: "Failed to Cancel Order",
        description: error.response?.data?.message || "An error occurred while cancelling the order",
        type: "error",
        duration: 5000,
      });
    },
  });
};

export default {
  useUserOrdersQuery,
  useOrderQuery,
  useCreateOrderMutation,
  useUpdateOrderStatusMutation,
  useCancelOrderMutation,
};
