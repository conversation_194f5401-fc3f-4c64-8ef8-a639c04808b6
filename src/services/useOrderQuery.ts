import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";
import { toaster } from "@/components/ui/toaster";

// Types
export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  quantity: number;
  price: number;
  product: {
    id: string;
    itemName: string;
    slug?: string;
    priceUSD: number;
    sellType: 'auction' | 'buy-now';
    status: string;
    images: Array<{
      id: string;
      imageUrl: string;
      altText?: string;
      sortOrder: number;
      isMain: boolean;
    }>;
  };
  createdAt: string;
}

export interface ShippingAddress {
  id: string;
  name: string;
  address: string;
  city: string;
  provinceRegion: string;
  zipCode: string;
  country: string;
}

export interface Order {
  id: string;
  userId: string;
  orderNumber: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: 'credit_card' | 'debit_card' | 'paypal' | 'bank_transfer';
  subtotal: number;
  shippingCost: number;
  tax: number;
  total: number;
  shippingAddressId?: string;
  notes?: string;
  shippingAddress?: ShippingAddress;
  items: OrderItem[];
  createdAt: string;
  updatedAt: string;
}

export interface OrdersListResponse {
  orders: Order[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface OrdersQueryParams {
  page?: number;
  limit?: number;
  status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded';
  sortBy?: 'createdAt' | 'total' | 'status' | 'orderNumber';
  sortOrder?: 'asc' | 'desc';
}

export interface UpdateOrderStatusData {
  status?: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded';
}

// Query Keys
export const orderQueryKeys = {
  all: ['orders'] as const,
  lists: () => [...orderQueryKeys.all, 'list'] as const,
  list: (params: OrdersQueryParams) => [...orderQueryKeys.lists(), params] as const,
  details: () => [...orderQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...orderQueryKeys.details(), id] as const,
};

// Get User Orders
export const useOrdersQuery = (params: OrdersQueryParams = {}) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: orderQueryKeys.list(params),
    queryFn: async (): Promise<OrdersListResponse> => {
      const searchParams = new URLSearchParams();
      
      if (params.page) searchParams.append('page', params.page.toString());
      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.status) searchParams.append('status', params.status);
      if (params.paymentStatus) searchParams.append('paymentStatus', params.paymentStatus);
      if (params.sortBy) searchParams.append('sortBy', params.sortBy);
      if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

      const response = await apiClient.get(`/orders?${searchParams.toString()}`);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Single Order
export const useOrderQuery = (orderId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: orderQueryKeys.detail(orderId),
    queryFn: async (): Promise<Order> => {
      const response = await apiClient.get(`/orders/${orderId}`);
      return response.data;
    },
    enabled: !!orderId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Update Order Status Mutation (Admin only)
export const useUpdateOrderStatusMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ orderId, data }: { orderId: string; data: UpdateOrderStatusData }): Promise<Order> => {
      const response = await apiClient.patch(`/orders/${orderId}/status`, data);
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch orders list
      queryClient.invalidateQueries({ queryKey: orderQueryKeys.lists() });
      
      // Update the specific order in cache
      queryClient.setQueryData(orderQueryKeys.detail(variables.orderId), data);
      
      toaster.create({
        title: "Success",
        description: "Order status updated successfully",
        type: "success",
      });
    },
    onError: (error: any) => {
      toaster.create({
        title: "Error",
        description: error.response?.data?.message || "Failed to update order status",
        type: "error",
      });
    },
  });
};

// Get Order Tracking
export const useOrderTrackingQuery = (orderId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: ['order-tracking', orderId],
    queryFn: async () => {
      const response = await apiClient.get(`/order-tracking/tracking/${orderId}`);
      return response.data;
    },
    enabled: !!orderId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute for real-time updates
  });
};

// Get User Orders with Tracking
export const useUserOrdersQuery = (params: OrdersQueryParams = {}) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: ['user-orders', params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();

      if (params.page) searchParams.append('page', params.page.toString());
      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.status) searchParams.append('status', params.status);
      if (params.paymentStatus) searchParams.append('paymentStatus', params.paymentStatus);

      const response = await apiClient.get(`/order-tracking/user?${searchParams.toString()}`);
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};
