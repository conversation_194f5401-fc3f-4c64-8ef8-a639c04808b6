import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuthenticatedApi } from './useAuthQuery';
import { SupportedCurrency } from '@/contexts/CurrencyLanguageContext';

interface ExchangeRateData {
  rate: number;
  lastUpdated: string;
  source: string;
}

/**
 * Global currency service with database caching
 * Fetch exchange rate once and use for all conversions
 */
export const useCurrencyService = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  // Get exchange rate from backend (with database caching)
  const { data: exchangeRateData, isLoading, error, refetch } = useQuery({
    queryKey: ['currency', 'exchange-rate'],
    queryFn: async (): Promise<ExchangeRateData> => {
      const response = await apiClient.get('/currency/rate');
      return response.data;
    },
    staleTime: 60 * 60 * 1000, // 1 hour - rate doesn't change frequently
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
    retry: 2,
    refetchOnWindowFocus: false,
  });

  /**
   * Convert price using cached exchange rate
   */
  const convertPrice = (amount: number, fromCurrency: SupportedCurrency, toCurrency: SupportedCurrency): number => {
    if (fromCurrency === toCurrency) {
      return amount;
    }

    if (!exchangeRateData?.rate) {
      const fallbackRate = 15500;
      if (fromCurrency === 'USD' && toCurrency === 'IDR') {
        const converted = amount * fallbackRate;
        return converted;
      } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
        const converted = amount / fallbackRate;
        return converted;
      }
      return amount;
    }

    const rate = exchangeRateData.rate; // USD to IDR rate

    if (fromCurrency === 'USD' && toCurrency === 'IDR') {
      const converted = amount * rate;
      return converted;
    } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
      const converted = amount / rate;
      return converted;
    }
    return amount;
  };

  /**
   * Format price with currency symbol
   */
  const formatPrice = (amount: number, currency: SupportedCurrency): string => {
    const formatter = new Intl.NumberFormat(currency === 'USD' ? 'en-US' : 'id-ID', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currency === 'IDR' ? 0 : 2,
      maximumFractionDigits: currency === 'IDR' ? 0 : 2,
    });

    return formatter.format(amount);
  };

  /**
   * Convert and format price in one step
   */
  const convertAndFormatPrice = (amount: number, fromCurrency: SupportedCurrency, toCurrency: SupportedCurrency): string => {
    const convertedAmount = convertPrice(amount, fromCurrency, toCurrency);
    return formatPrice(convertedAmount, toCurrency);
  };

  /**
   * Get exchange rate info
   */
  const getExchangeRateInfo = () => {
    if (!exchangeRateData) return null;

    return {
      rate: exchangeRateData.rate,
      formatted: `1 USD = ${exchangeRateData.rate.toLocaleString()} IDR`,
      lastUpdated: exchangeRateData.lastUpdated,
      source: exchangeRateData.source,
      isStale: exchangeRateData.source === 'fallback'
    };
  };

  /**
   * Refresh exchange rate
   */
  const refreshExchangeRate = async () => {
    await queryClient.invalidateQueries({ queryKey: ['currency', 'exchange-rate'] });
    await refetch();
  };

  /**
   * Invalidate all currency-related queries when currency changes
   */
  const invalidateCurrencyQueries = async () => {
    await queryClient.invalidateQueries({ queryKey: ['products'] });
    await queryClient.invalidateQueries({ queryKey: ['currency'] });
  };

  return {
    // Data
    exchangeRate: exchangeRateData?.rate || 15500, // updated fallback rate
    lastUpdated: exchangeRateData?.lastUpdated,
    source: exchangeRateData?.source || 'fallback',
    isLoading,
    error,
    isReady: !!exchangeRateData && !isLoading,

    // Functions
    convertPrice,
    formatPrice,
    convertAndFormatPrice,
    getExchangeRateInfo,
    refreshExchangeRate,
    invalidateCurrencyQueries,
  };
};

export default useCurrencyService;
