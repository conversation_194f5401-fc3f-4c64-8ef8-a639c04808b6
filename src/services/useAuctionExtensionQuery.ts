import { useQuery } from '@tanstack/react-query';
import { useAuthenticatedApi } from './useAuthQuery';

// Types
export interface AuctionExtensionLog {
  id: string;
  productId: string;
  previousEndDate: string;
  newEndDate: string;
  extendedMinutes: number;
  triggerBidAmount: number;
  triggeredBy: 'manual' | 'auto-bid';
  triggeredBidder?: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  } | null;
  extensionReason: string;
  createdAt: string;
}

export interface ProductExtensionLogs {
  productId: string;
  extensionLogs: AuctionExtensionLog[];
  totalExtensions: number;
}

export interface ExtensionStats {
  productId: string;
  totalExtensions: number;
  totalExtendedMinutes: number;
  averageTriggerBid: number;
  highestTriggerBid: number;
  extensionsByType: Array<{
    triggeredBy: string;
    count: number;
  }>;
}

export interface AllExtensionLogs {
  extensionLogs: Array<AuctionExtensionLog & {
    product: {
      id: string;
      itemName: string;
      slug: string | null;
    };
  }>;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    limit: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Query Keys
export const auctionExtensionQueryKeys = {
  all: ['auction-extensions'] as const,
  productLogs: (productId: string) => [...auctionExtensionQueryKeys.all, 'product', productId] as const,
  productStats: (productId: string) => [...auctionExtensionQueryKeys.all, 'stats', productId] as const,
  allLogs: (filters?: Record<string, any>) => [...auctionExtensionQueryKeys.all, 'logs', filters] as const,
};

// Get extension logs for a specific product
export const useProductExtensionLogsQuery = (productId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: auctionExtensionQueryKeys.productLogs(productId),
    queryFn: async (): Promise<ProductExtensionLogs> => {
      const response = await apiClient.get(`/auction-extensions/product/${productId}/logs`);
      return response?.data ?? null;
    },
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get extension statistics for a product
export const useProductExtensionStatsQuery = (productId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: auctionExtensionQueryKeys.productStats(productId),
    queryFn: async (): Promise<ExtensionStats> => {
      const response = await apiClient.get(`/auction-extensions/product/${productId}/stats`);
      return response?.data;
    },
    enabled: !!productId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Get all extension logs with pagination and filters
export const useAllExtensionLogsQuery = (params?: {
  page?: number;
  limit?: number;
  productId?: string;
  triggeredBy?: 'manual' | 'auto-bid';
}) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: auctionExtensionQueryKeys.allLogs(params),
    queryFn: async (): Promise<AllExtensionLogs> => {
      const searchParams = new URLSearchParams();
      
      if (params?.page) searchParams.append('page', params.page.toString());
      if (params?.limit) searchParams.append('limit', params.limit.toString());
      if (params?.productId) searchParams.append('productId', params.productId);
      if (params?.triggeredBy) searchParams.append('triggeredBy', params.triggeredBy);

      const response = await apiClient.get(`/auction-extensions/logs?${searchParams.toString()}`);
      return response.data.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};
