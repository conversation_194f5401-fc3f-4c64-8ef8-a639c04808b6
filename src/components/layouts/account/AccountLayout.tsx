'use client'
import React from 'react'
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Heading,
  Button,
  Icon,
  Badge,
  Separator,
  Card,
  Grid,
  GridItem,
  Avatar,
  Skeleton,
} from '@chakra-ui/react'
import {
  FaShoppingBag,
  FaGavel,
  FaStore,
  FaCog,
  FaSignOutAlt,
  FaHome,
  FaCalendarAlt,
} from 'react-icons/fa'
import { useTranslations } from 'next-intl'
import { useSession, signOut } from 'next-auth/react'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'

interface AccountLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
}

interface MenuItem {
  key: string
  label: string
  icon: React.ElementType
  href: string
  badge?: number
  color?: string
}

const AccountLayout: React.FC<AccountLayoutProps> = ({
  children,
  title,
  description
}) => {
  const t = useTranslations()
  const { data: session, status: sessionStatus } = useSession()
  const router = useRouter()
  const pathname = usePathname()

  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      label: t('Account.dashboard'),
      icon: FaHome,
      href: '/account/summary',
      color: 'blue.500'
    },
    {
      key: 'buying',
      label: t('Account.buying'),
      icon: FaShoppingBag,
      href: '/account/buying',
      color: 'green.500'
    },
    {
      key: 'bidding',
      label: t('Account.bidding'),
      icon: FaGavel,
      href: '/account/bidding',
      color: 'purple.500'
    },
    {
      key: 'selling',
      label: t('Account.selling'),
      icon: FaStore,
      href: '/account/selling',
      color: 'orange.500'
    },
    {
      key: 'settings',
      label: t('Account.settings'),
      icon: FaCog,
      href: '/account/setting',
      color: 'gray.500'
    }
  ]

  const isActiveRoute = (href: string) => {
    return pathname.includes(href)
  }

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/' })
  }

  const getMemberSinceDate = () => {
    // For now, return a default message since createdAt is not available in session
    // TODO: Fetch user profile data to get actual creation date
    return t('Account.memberSince') || 'Member since 2024'
  }

  if (sessionStatus === "loading") {
    return (
      <VStack gap={6} align="stretch">
        <Card.Root bg="white" shadow="sm" borderRadius="xl">
          <Card.Body p={6}>
            <Skeleton height="400px" />
            <Skeleton height="400px" mt={4} />
            <Skeleton height="400px" mt={2} />
          </Card.Body>
        </Card.Root>
      </VStack>
    )
  }

  if (!session) {
    return (
      <Container maxW="6xl" py={8}>
        <Box textAlign="center" py={12}>
          <Heading size="lg" mb={4}>
            {t('Auth.pleaseLogin')}
          </Heading>
          <Button colorScheme="blue" onClick={() => router.push('/auth/signin')}>
            {t('Button.login')}
          </Button>
        </Box>
      </Container>
    )
  }

  return (
    <Box bg="gray.50" minH="100vh">
      <Container maxW="7xl" py={6}>
        <Grid templateColumns={{ base: '1fr', lg: '280px 1fr' }} gap={6}>
          {/* Sidebar */}
          <GridItem>
            <VStack gap={6} align="stretch">
              {/* Profile Card */}
              <Card.Root bg="white" shadow="sm" borderRadius="xl" overflow="hidden">
                <Card.Body p={6}>
                  <VStack gap={4} align="center" textAlign="center">
                    <Avatar.Root variant="outline" size="xl" borderWidth={2} borderColor="black">
                      <Avatar.Fallback name={session?.user?.name ?? ""} />
                    </Avatar.Root>
                    <VStack gap={1}>
                      <Heading size="md" color="gray.800">
                        {session?.user.firstName} {session?.user.lastName}
                      </Heading>
                      <Text color="gray.600" fontSize="sm">
                        {session?.user.email}
                      </Text>
                      <HStack gap={2} align="center">
                        <Icon color="gray.400" fontSize="xs">
                          <FaCalendarAlt />
                        </Icon>
                        <Text color="gray.500" fontSize="xs">
                          {getMemberSinceDate()}
                        </Text>
                      </HStack>
                    </VStack>
                  </VStack>
                </Card.Body>
              </Card.Root>

              {/* Navigation Menu */}
              <Card.Root bg="white" shadow="sm" borderRadius="xl" overflow="hidden">
                <Card.Body p={0}>
                  <VStack gap={0} align="stretch">
                    {menuItems.map((item, index) => (
                      <React.Fragment key={item.key}>
                        <Link href={item.href}>
                          <Box
                            p={4}
                            cursor="pointer"
                            transition="all 0.2s"
                            bg={isActiveRoute(item.href) ? 'blue.50' : 'transparent'}
                            borderLeft={isActiveRoute(item.href) ? '4px solid' : '4px solid transparent'}
                            borderLeftColor={isActiveRoute(item.href) ? 'blue.500' : 'transparent'}
                            _hover={{
                              bg: isActiveRoute(item.href) ? 'blue.50' : 'gray.50',
                              transform: 'translateX(2px)'
                            }}
                          >
                            <HStack gap={3}>
                              <Icon
                                color={isActiveRoute(item.href) ? 'blue.500' : item.color}
                                fontSize="lg"
                              >
                                <item.icon />
                              </Icon>
                              <Text
                                fontWeight={isActiveRoute(item.href) ? 'semibold' : 'medium'}
                                color={isActiveRoute(item.href) ? 'blue.700' : 'gray.700'}
                                fontSize="sm"
                              >
                                {item.label}
                              </Text>
                              {item.badge && (
                                <Badge colorScheme="red" size="sm" borderRadius="full">
                                  {item.badge}
                                </Badge>
                              )}
                            </HStack>
                          </Box>
                        </Link>
                        {index < menuItems.length - 1 && <Separator />}
                      </React.Fragment>
                    ))}

                    <Separator />

                    {/* Logout Button */}
                    <Box
                      p={4}
                      cursor="pointer"
                      transition="all 0.2s"
                      _hover={{ bg: 'red.50', transform: 'translateX(2px)' }}
                      onClick={handleLogout}
                    >
                      <HStack gap={3}>
                        <Icon color="red.500" fontSize="lg">
                          <FaSignOutAlt />
                        </Icon>
                        <Text fontWeight="medium" color="red.600" fontSize="sm">
                          {t('Account.logout')}
                        </Text>
                      </HStack>
                    </Box>
                  </VStack>
                </Card.Body>
              </Card.Root>
            </VStack>
          </GridItem>

          {/* Main Content */}
          <GridItem>
            <VStack gap={6} align="stretch">
              {/* Page Header */}
              {(title || description) && (
                <Card.Root bg="white" shadow="sm" borderRadius="xl">
                  <Card.Body p={6}>
                    <VStack gap={2} align="start">
                      {title && (
                        <Heading size="xl" color="gray.800">
                          {title}
                        </Heading>
                      )}
                      {description && (
                        <Text color="gray.600" fontSize="lg">
                          {description}
                        </Text>
                      )}
                    </VStack>
                  </Card.Body>
                </Card.Root>
              )}

              {/* Page Content */}
              <Box>
                {children}
              </Box>
            </VStack>
          </GridItem>
        </Grid>
      </Container>
    </Box>
  )
}

export default AccountLayout
