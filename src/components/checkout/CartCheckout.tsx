'use client';

import {
  <PERSON>,
  VStack,
  HStack,
  Text,
  Image,
  Divider,
  Button,
  useToast,
  Card,
  CardBody,
  Badge,
  Flex
} from '@chakra-ui/react';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCreateOrderFromCartMutation } from '@/services/useCheckoutQuery';
import { formatCurrency } from '@/utils/currency';

interface CartCheckoutProps {
  validationData: {
    cart: {
      id: string;
      items: Array<{
        id: string;
        quantity: number;
        price: string;
        product: {
          id: string;
          itemName: string;
          images: Array<{ imageUrl: string }>;
          priceUSD: string;
        };
      }>;
      subtotal: number;
      itemCount: number;
    };
  };
}

export const CartCheckout = ({ validationData }: CartCheckoutProps) => {
  const [selectedShippingAddress, setSelectedShippingAddress] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const router = useRouter();
  const toast = useToast();
  
  const createOrderMutation = useCreateOrderFromCartMutation();
  
  const { cart } = validationData;
  const subtotal = cart.subtotal;
  const shippingCost = 50000; // IDR 50k shipping
  const total = subtotal + shippingCost;

  const handleCreateOrder = async () => {
    if (!selectedShippingAddress) {
      toast({
        title: "Shipping Address Required",
        description: "Please select a shipping address",
        status: "warning",
        duration: 3000,
      });
      return;
    }

    setIsProcessing(true);
    
    try {
      const result = await createOrderMutation.mutateAsync({
        shippingAddressId: selectedShippingAddress,
        notes: ''
      });
      
      toast({
        title: "Order Created",
        description: "Your order has been created successfully",
        status: "success",
        duration: 3000,
      });
      
      router.push(`/orders/${result.id}`);
    } catch (error) {
      toast({
        title: "Order Failed",
        description: "Failed to create order. Please try again.",
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Box maxW="800px" mx="auto" p={6}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Box>
          <Text fontSize="2xl" fontWeight="bold" mb={2}>
            Checkout from Cart
          </Text>
          <Text color="gray.600">
            Review your items and complete your purchase
          </Text>
        </Box>

        {/* Cart Items */}
        <Card>
          <CardBody>
            <Text fontSize="lg" fontWeight="semibold" mb={4}>
              Order Items ({cart.itemCount} items)
            </Text>
            
            <VStack spacing={4} divider={<Divider />}>
              {cart.items.map((item) => (
                <HStack key={item.id} spacing={4} w="full">
                  <Image
                    src={item.product.images[0]?.imageUrl || '/placeholder.jpg'}
                    alt={item.product.itemName}
                    boxSize="80px"
                    objectFit="cover"
                    borderRadius="md"
                  />
                  
                  <VStack align="start" flex={1} spacing={1}>
                    <Text fontWeight="medium">{item.product.itemName}</Text>
                    <HStack>
                      <Badge colorScheme="blue">Qty: {item.quantity}</Badge>
                      <Text fontSize="sm" color="gray.600">
                        {formatCurrency(Number(item.price), 'IDR')} each
                      </Text>
                    </HStack>
                  </VStack>
                  
                  <Text fontWeight="bold">
                    {formatCurrency(Number(item.price) * item.quantity, 'IDR')}
                  </Text>
                </HStack>
              ))}
            </VStack>
          </CardBody>
        </Card>

        {/* Order Summary */}
        <Card>
          <CardBody>
            <Text fontSize="lg" fontWeight="semibold" mb={4}>
              Order Summary
            </Text>
            
            <VStack spacing={2} align="stretch">
              <Flex justify="space-between">
                <Text>Subtotal ({cart.itemCount} items)</Text>
                <Text>{formatCurrency(subtotal, 'IDR')}</Text>
              </Flex>
              
              <Flex justify="space-between">
                <Text>Shipping</Text>
                <Text>{formatCurrency(shippingCost, 'IDR')}</Text>
              </Flex>
              
              <Divider />
              
              <Flex justify="space-between" fontWeight="bold" fontSize="lg">
                <Text>Total</Text>
                <Text color="blue.600">{formatCurrency(total, 'IDR')}</Text>
              </Flex>
            </VStack>
          </CardBody>
        </Card>

        {/* Action Buttons */}
        <HStack spacing={4}>
          <Button
            variant="outline"
            onClick={() => router.push('/cart')}
            flex={1}
          >
            Back to Cart
          </Button>
          
          <Button
            colorScheme="blue"
            onClick={handleCreateOrder}
            isLoading={isProcessing}
            loadingText="Creating Order..."
            flex={2}
          >
            Create Order
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
};
