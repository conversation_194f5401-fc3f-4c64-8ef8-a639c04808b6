'use client';

import {
  Box,
  VStack,
  HStack,
  Text,
  Image,
  Button,
  useToast,
  Card,
  CardBody,
  Badge,
  Flex,
  Divider,
  Alert,
  AlertIcon,
  Progress
} from '@chakra-ui/react';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCreateAuctionOrderMutation } from '@/services/useAuctionPaymentQuery';
import { formatCurrency } from '@/utils/currency';

interface BiddingCheckoutProps {
  validationData: {
    product: {
      id: string;
      itemName: string;
      description: string;
      images: Array<{ imageUrl: string }>;
      seller: {
        id: string;
        firstName: string;
        lastName: string;
      };
      auctionEndDate: string;
    };
    winningBid: {
      id: string;
      amount: number;
      bidderId: string;
      bidder: {
        id: string;
        firstName: string;
        lastName: string;
      };
      createdAt: string;
    };
    paymentDeadline: string;
    hoursRemaining: number;
    isUrgent: boolean;
  };
}

export const BiddingCheckout = ({ validationData }: BiddingCheckoutProps) => {
  const [selectedShippingAddress, setSelectedShippingAddress] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const router = useRouter();
  const toast = useToast();
  
  const createAuctionOrderMutation = useCreateAuctionOrderMutation();
  
  const { product, winningBid, paymentDeadline, hoursRemaining, isUrgent } = validationData;
  const winningAmount = winningBid.amount;
  const shippingCost = 50000; // IDR 50k shipping
  const total = winningAmount + shippingCost;

  const handleCreateOrder = async () => {
    if (!selectedShippingAddress) {
      toast({
        title: "Shipping Address Required",
        description: "Please select a shipping address",
        status: "warning",
        duration: 3000,
      });
      return;
    }

    setIsProcessing(true);
    
    try {
      const result = await createAuctionOrderMutation.mutateAsync({
        productId: product.id,
        bidId: winningBid.id,
        shippingAddressId: selectedShippingAddress,
        notes: ''
      });
      
      toast({
        title: "Order Created",
        description: "Your auction order has been created successfully",
        status: "success",
        duration: 3000,
      });
      
      router.push(`/orders/${result.id}`);
    } catch (error) {
      toast({
        title: "Order Failed",
        description: "Failed to create auction order. Please try again.",
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Calculate urgency level for progress bar
  const urgencyLevel = hoursRemaining <= 24 ? 'red' : hoursRemaining <= 48 ? 'yellow' : 'green';
  const progressValue = Math.max(0, Math.min(100, (hoursRemaining / 72) * 100)); // 72 hours = 100%

  return (
    <Box maxW="800px" mx="auto" p={6}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Box>
          <Text fontSize="2xl" fontWeight="bold" mb={2}>
            Auction Winner Checkout
          </Text>
          <Text color="gray.600">
            Congratulations! Complete your payment for this auction win
          </Text>
        </Box>

        {/* Payment Deadline Warning */}
        <Alert status={isUrgent ? 'error' : 'warning'} borderRadius="md">
          <AlertIcon />
          <VStack align="start" spacing={2} flex={1}>
            <Text fontWeight="bold">
              {isUrgent ? 'Urgent: Payment Deadline Approaching!' : 'Payment Deadline'}
            </Text>
            <Text fontSize="sm">
              You have {hoursRemaining} hours remaining to complete payment
            </Text>
            <Text fontSize="xs" color="gray.600">
              Deadline: {new Date(paymentDeadline).toLocaleString()}
            </Text>
            <Progress 
              value={progressValue} 
              colorScheme={urgencyLevel} 
              size="sm" 
              w="full" 
              mt={2}
            />
          </VStack>
        </Alert>

        {/* Auction Details */}
        <Card>
          <CardBody>
            <Text fontSize="lg" fontWeight="semibold" mb={4}>
              Auction Details
            </Text>
            
            <HStack spacing={6} align="start">
              <Image
                src={product.images[0]?.imageUrl || '/placeholder.jpg'}
                alt={product.itemName}
                boxSize="150px"
                objectFit="cover"
                borderRadius="md"
              />
              
              <VStack align="start" flex={1} spacing={3}>
                <Text fontSize="xl" fontWeight="bold">
                  {product.itemName}
                </Text>
                
                <Text color="gray.600" noOfLines={3}>
                  {product.description}
                </Text>
                
                <HStack spacing={3}>
                  <Badge colorScheme="purple">Auction Winner</Badge>
                  <Badge colorScheme="green">Auction Ended</Badge>
                </HStack>
                
                <VStack align="start" spacing={1}>
                  <Text fontSize="sm" color="gray.600">
                    Sold by: {product.seller.firstName} {product.seller.lastName}
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    Auction ended: {new Date(product.auctionEndDate).toLocaleString()}
                  </Text>
                </VStack>
                
                <Text fontSize="2xl" fontWeight="bold" color="purple.600">
                  Winning Bid: {formatCurrency(winningAmount, 'IDR')}
                </Text>
              </VStack>
            </HStack>
          </CardBody>
        </Card>

        {/* Order Summary */}
        <Card>
          <CardBody>
            <Text fontSize="lg" fontWeight="semibold" mb={4}>
              Payment Summary
            </Text>
            
            <VStack spacing={2} align="stretch">
              <Flex justify="space-between">
                <Text>Winning Bid Amount</Text>
                <Text>{formatCurrency(winningAmount, 'IDR')}</Text>
              </Flex>
              
              <Flex justify="space-between">
                <Text>Shipping</Text>
                <Text>{formatCurrency(shippingCost, 'IDR')}</Text>
              </Flex>
              
              <Divider />
              
              <Flex justify="space-between" fontWeight="bold" fontSize="lg">
                <Text>Total Payment</Text>
                <Text color="purple.600">{formatCurrency(total, 'IDR')}</Text>
              </Flex>
            </VStack>
          </CardBody>
        </Card>

        {/* Action Buttons */}
        <HStack spacing={4}>
          <Button
            variant="outline"
            onClick={() => router.push(`/products/${product.id}`)}
            flex={1}
          >
            Back to Product
          </Button>
          
          <Button
            colorScheme="purple"
            onClick={handleCreateOrder}
            isLoading={isProcessing}
            loadingText="Creating Order..."
            flex={2}
            size="lg"
          >
            Complete Payment
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
};
