'use client';

import {
  Box,
  VStack,
  HStack,
  Text,
  Image,
  Button,
  useToast,
  Card,
  CardBody,
  Badge,
  Flex,
  Divider
} from '@chakra-ui/react';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useBuyNowMutation } from '@/services/useCheckoutQuery';
import { formatCurrency } from '@/utils/currency';

interface BuyNowCheckoutProps {
  validationData: {
    product: {
      id: string;
      itemName: string;
      description: string;
      priceUSD: number;
      images: Array<{ imageUrl: string }>;
      seller: {
        id: string;
        firstName: string;
        lastName: string;
      };
    };
  };
}

export const BuyNowCheckout = ({ validationData }: BuyNowCheckoutProps) => {
  const [selectedShippingAddress, setSelectedShippingAddress] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const router = useRouter();
  const toast = useToast();
  
  const buyNowMutation = useBuyNowMutation();
  
  const { product } = validationData;
  const productPrice = product.priceUSD;
  const shippingCost = 50000; // IDR 50k shipping
  const total = productPrice + shippingCost;

  const handleBuyNow = async () => {
    if (!selectedShippingAddress) {
      toast({
        title: "Shipping Address Required",
        description: "Please select a shipping address",
        status: "warning",
        duration: 3000,
      });
      return;
    }

    setIsProcessing(true);
    
    try {
      const result = await buyNowMutation.mutateAsync({
        productId: product.id,
        shippingAddressId: selectedShippingAddress,
        quantity: 1,
        notes: ''
      });
      
      toast({
        title: "Order Created",
        description: "Your order has been created successfully",
        status: "success",
        duration: 3000,
      });
      
      router.push(`/orders/${result.id}`);
    } catch (error) {
      toast({
        title: "Order Failed",
        description: "Failed to create order. Please try again.",
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Box maxW="800px" mx="auto" p={6}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Box>
          <Text fontSize="2xl" fontWeight="bold" mb={2}>
            Buy Now Checkout
          </Text>
          <Text color="gray.600">
            Complete your purchase for this item
          </Text>
        </Box>

        {/* Product Details */}
        <Card>
          <CardBody>
            <Text fontSize="lg" fontWeight="semibold" mb={4}>
              Product Details
            </Text>
            
            <HStack spacing={6} align="start">
              <Image
                src={product.images[0]?.imageUrl || '/placeholder.jpg'}
                alt={product.itemName}
                boxSize="150px"
                objectFit="cover"
                borderRadius="md"
              />
              
              <VStack align="start" flex={1} spacing={3}>
                <Text fontSize="xl" fontWeight="bold">
                  {product.itemName}
                </Text>
                
                <Text color="gray.600" noOfLines={3}>
                  {product.description}
                </Text>
                
                <HStack>
                  <Badge colorScheme="green">Buy Now</Badge>
                  <Text fontSize="sm" color="gray.600">
                    Sold by: {product.seller.firstName} {product.seller.lastName}
                  </Text>
                </HStack>
                
                <Text fontSize="2xl" fontWeight="bold" color="blue.600">
                  {formatCurrency(productPrice, 'IDR')}
                </Text>
              </VStack>
            </HStack>
          </CardBody>
        </Card>

        {/* Order Summary */}
        <Card>
          <CardBody>
            <Text fontSize="lg" fontWeight="semibold" mb={4}>
              Order Summary
            </Text>
            
            <VStack spacing={2} align="stretch">
              <Flex justify="space-between">
                <Text>Product Price</Text>
                <Text>{formatCurrency(productPrice, 'IDR')}</Text>
              </Flex>
              
              <Flex justify="space-between">
                <Text>Quantity</Text>
                <Text>1</Text>
              </Flex>
              
              <Flex justify="space-between">
                <Text>Shipping</Text>
                <Text>{formatCurrency(shippingCost, 'IDR')}</Text>
              </Flex>
              
              <Divider />
              
              <Flex justify="space-between" fontWeight="bold" fontSize="lg">
                <Text>Total</Text>
                <Text color="blue.600">{formatCurrency(total, 'IDR')}</Text>
              </Flex>
            </VStack>
          </CardBody>
        </Card>

        {/* Action Buttons */}
        <HStack spacing={4}>
          <Button
            variant="outline"
            onClick={() => router.push(`/products/${product.id}`)}
            flex={1}
          >
            Back to Product
          </Button>
          
          <Button
            colorScheme="blue"
            onClick={handleBuyNow}
            isLoading={isProcessing}
            loadingText="Creating Order..."
            flex={2}
          >
            Buy Now
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
};
