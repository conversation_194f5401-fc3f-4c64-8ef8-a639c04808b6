'use client';

import { useSearchParams } from 'next/navigation';
import { useMemo } from 'react';

export type CheckoutType = 'cart' | 'buy-now' | 'bidding';

export interface CheckoutParams {
  type: CheckoutType;
  productId?: string;
  bidId?: string;
}

export const useCheckoutParams = (): CheckoutParams => {
  const searchParams = useSearchParams();
  
  return useMemo(() => {
    const productId = searchParams.get('productId');
    const typeParam = searchParams.get('type');
    
    // Determine checkout type based on parameters
    if (productId && typeParam === 'bidding') {
      return {
        type: 'bidding',
        productId,
        bidId: searchParams.get('bidId') || undefined
      };
    }
    
    if (productId && (typeParam === 'buy-now' || !typeParam)) {
      return {
        type: 'buy-now',
        productId
      };
    }
    
    // Default to cart checkout if no specific parameters
    return {
      type: 'cart'
    };
  }, [searchParams]);
};

export const CheckoutTypeDetector = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};
