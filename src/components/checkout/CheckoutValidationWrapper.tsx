'use client';

import { <PERSON>, <PERSON><PERSON>, Spinner, VStack, Text, Button } from '@chakra-ui/react';
import { useCheckoutValidationQuery } from '@/services/useCheckoutQuery';
import { CheckoutType } from './CheckoutTypeDetector';
import { useRouter } from 'next/navigation';

interface CheckoutValidationWrapperProps {
  checkoutType: CheckoutType;
  productId?: string;
  bidId?: string;
  children: (data: any) => React.ReactNode;
}

export const CheckoutValidationWrapper = ({
  checkoutType,
  productId,
  bidId,
  children
}: CheckoutValidationWrapperProps) => {
  const router = useRouter();
  
  const {
    data: validationData,
    isLoading,
    error,
    refetch
  } = useCheckoutValidationQuery(checkoutType, productId, bidId);

  if (isLoading) {
    return (
      <Box textAlign="center" py={10}>
        <VStack spacing={4}>
          <Spinner size="lg" color="blue.500" />
          <Text>Validating checkout...</Text>
        </VStack>
      </Box>
    );
  }

  if (error || !validationData?.status) {
    const errorMessage = validationData?.message || 
      (error as any)?.response?.data?.message || 
      'Failed to validate checkout';
    
    const errorDetails = validationData?.data?.errors || [];
    
    return (
      <Box maxW="600px" mx="auto" p={6}>
        <Alert status="error" borderRadius="md" flexDirection="column" alignItems="start">
          <Text fontWeight="bold" mb={2}>
            Checkout Validation Failed
          </Text>
          <Text mb={3}>{errorMessage}</Text>
          
          {errorDetails.length > 0 && (
            <VStack align="start" spacing={1} mb={4}>
              {errorDetails.map((detail: string, index: number) => (
                <Text key={index} fontSize="sm" color="red.600">
                  • {detail}
                </Text>
              ))}
            </VStack>
          )}
          
          <VStack spacing={2} w="full">
            <Button onClick={() => refetch()} colorScheme="red" variant="outline" size="sm">
              Try Again
            </Button>
            
            {checkoutType === 'cart' && (
              <Button onClick={() => router.push('/cart')} size="sm">
                Go to Cart
              </Button>
            )}
            
            {(checkoutType === 'buy-now' || checkoutType === 'bidding') && productId && (
              <Button onClick={() => router.push(`/products/${productId}`)} size="sm">
                Back to Product
              </Button>
            )}
            
            <Button onClick={() => router.push('/')} variant="ghost" size="sm">
              Go to Home
            </Button>
          </VStack>
        </Alert>
      </Box>
    );
  }

  // Validation successful, render children with validated data
  return <>{children(validationData.data)}</>;
};
