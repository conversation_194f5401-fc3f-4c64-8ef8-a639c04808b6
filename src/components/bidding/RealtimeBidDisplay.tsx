'use client'

import { <PERSON>, Text, Badge, HStack, VStack, I<PERSON>, Spinner } from '@chakra-ui/react'
import { useRealtimeBidding } from '@/hooks/useRealtimeBidding'
import { formatCurrency } from '@/utils/currency'
import { FiWifi, FiWifiOff, FiClock, FiTrendingUp } from 'react-icons/fi'
import { useEffect, useState } from 'react'

interface RealtimeBidDisplayProps {
  productId: string
  initialBid?: number
  currency?: string
  showConnectionStatus?: boolean
  compact?: boolean
}

export const RealtimeBidDisplay = ({
  productId,
  initialBid,
  currency = 'USD',
  showConnectionStatus = true,
  compact = false
}: RealtimeBidDisplayProps) => {
  const {
    currentBid,
    bidCount,
    lastBidder,
    lastUpdate,
    isConnected,
    isRealtimeEnabled,
    error,
    refreshBidData
  } = useRealtimeBidding(productId, initialBid)

  const [isUpdating, setIsUpdating] = useState(false)
  const [lastBidAmount, setLastBidAmount] = useState<number | null>(null)

  // Show update animation when bid changes
  useEffect(() => {
    if (currentBid && currentBid !== lastBidAmount) {
      setIsUpdating(true)
      setLastBidAmount(currentBid)
      
      const timer = setTimeout(() => {
        setIsUpdating(false)
      }, 2000)
      
      return () => clearTimeout(timer)
    }
  }, [currentBid, lastBidAmount])

  const displayBid = currentBid || initialBid || 0
  const formattedBid = formatCurrency(displayBid, currency)

  if (compact) {
    return (
      <HStack gap={2} align="center">
        <Text 
          fontSize="lg" 
          fontWeight="bold"
          color={isUpdating ? 'green.500' : 'gray.900'}
          transition="color 0.3s ease"
        >
          {formattedBid}
        </Text>
        {isUpdating && (
          <Icon as={FiTrendingUp} color="green.500" boxSize={4} />
        )}
        {showConnectionStatus && (
          <Icon 
            as={isRealtimeEnabled ? FiWifi : FiWifiOff} 
            color={isRealtimeEnabled ? 'green.500' : 'red.500'} 
            boxSize={3}
          />
        )}
      </HStack>
    )
  }

  return (
    <Box
      p={4}
      borderWidth={1}
      borderRadius="lg"
      borderColor={isUpdating ? 'green.200' : 'gray.200'}
      bg={isUpdating ? 'green.50' : 'white'}
      transition="all 0.3s ease"
      position="relative"
    >
      {/* Connection Status */}
      {showConnectionStatus && (
        <HStack justify="space-between" mb={3}>
          <Badge
            colorScheme={isRealtimeEnabled ? 'green' : 'red'}
            variant="subtle"
            fontSize="xs"
          >
            <HStack gap={1}>
              <Icon as={isRealtimeEnabled ? FiWifi : FiWifiOff} boxSize={3} />
              <Text>{isRealtimeEnabled ? 'Live' : 'Offline'}</Text>
            </HStack>
          </Badge>
          
          {error && (
            <Badge colorScheme="orange" variant="subtle" fontSize="xs">
              Connection Issue
            </Badge>
          )}
        </HStack>
      )}

      <VStack align="start" gap={2}>
        <HStack gap={3} align="center">
          <VStack align="start" gap={0}>
            <Text fontSize="sm" color="gray.600">
              Current Bid
            </Text>
            <HStack gap={2} align="center">
              <Text 
                fontSize="2xl" 
                fontWeight="bold"
                color={isUpdating ? 'green.600' : 'gray.900'}
                transition="color 0.3s ease"
              >
                {formattedBid}
              </Text>
              {isUpdating && (
                <Icon as={FiTrendingUp} color="green.500" boxSize={5} />
              )}
            </HStack>
          </VStack>
          
          {isUpdating && (
            <Spinner size="sm" color="green.500" />
          )}
        </HStack>

        {/* Bid Statistics */}
        <HStack gap={4} fontSize="sm" color="gray.600">
          <HStack gap={1}>
            <Text fontWeight="medium">{bidCount}</Text>
            <Text>bids</Text>
          </HStack>
          
          {lastBidder && (
            <HStack gap={1}>
              <Text>by</Text>
              <Text fontWeight="medium" color="blue.600">
                {lastBidder}
              </Text>
            </HStack>
          )}
          
          {lastUpdate && (
            <HStack gap={1}>
              <Icon as={FiClock} boxSize={3} />
              <Text>
                {new Date(lastUpdate).toLocaleTimeString()}
              </Text>
            </HStack>
          )}
        </HStack>

        {/* Manual Refresh Option */}
        {!isRealtimeEnabled && (
          <Text 
            fontSize="xs" 
            color="blue.500" 
            cursor="pointer"
            onClick={refreshBidData}
            _hover={{ textDecoration: 'underline' }}
          >
            Click to refresh bid data
          </Text>
        )}
      </VStack>

      {/* Update Indicator */}
      {isUpdating && (
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          h="2px"
          bg="green.400"
          borderTopRadius="lg"
          animation="pulse 1s ease-in-out infinite"
        />
      )}
    </Box>
  )
}

export default RealtimeBidDisplay
