'use client'

import React, { useEffect, useState } from 'react'
import { useWebSocketContextRequired } from '@/contexts/WebSocketContext'
import { debugWebSocket } from '@/utils/websocket-debug'

export const WebSocketTest: React.FC = () => {
  const {
    isConnected,
    connectionStatus,
    connectionStats,
    reconnect,
    disconnect,
    sendMessage,
    getConnectionHealth
  } = useWebSocketContextRequired()
  
  const [testMessage, setTestMessage] = useState('')
  const [debugInfo, setDebugInfo] = useState<any>(null)

  useEffect(() => {
    // Update debug info every 2 seconds
    const interval = setInterval(() => {
      const summary = debugWebSocket.getSummary()
      const health = getConnectionHealth()
      setDebugInfo({ summary, health })
    }, 2000)

    return () => clearInterval(interval)
  }, [getConnectionHealth])

  const handleSendTest = () => {
    if (testMessage.trim()) {
      sendMessage({
        type: 'test',
        data: testMessage,
        timestamp: Date.now()
      })
      setTestMessage('')
    }
  }

  const handleForceConnect = () => {
    console.log('🔄 Force connecting WebSocket...')
    reconnect()
  }

  const handleForceDisconnect = () => {
    console.log('🔌 Force disconnecting WebSocket...')
    disconnect()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-600'
      case 'connecting': return 'text-yellow-600'
      case 'disconnected': return 'text-gray-600'
      case 'error': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }



  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">WebSocket Connection Test</h2>
      
      {/* Connection Status */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Connection Status</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="font-medium">Status: </span>
            <span className={`font-bold ${getStatusColor(connectionStatus)}`}>
              {connectionStatus.toUpperCase()}
            </span>
          </div>
          <div>
            <span className="font-medium">Connected: </span>
            <span className={isConnected ? 'text-green-600' : 'text-red-600'}>
              {isConnected ? '✅ Yes' : '❌ No'}
            </span>
          </div>
        </div>
      </div>

      {/* Connection Stats */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Connection Statistics</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>Reconnect Attempts: <span className="font-mono">{connectionStats.reconnectAttempts}</span></div>
          <div>Messages Sent: <span className="font-mono">{connectionStats.messagesSent}</span></div>
          <div>Messages Received: <span className="font-mono">{connectionStats.messagesReceived}</span></div>
          <div>Uptime: <span className="font-mono">{connectionStats.uptime}ms</span></div>
          <div>Latency: <span className="font-mono">{connectionStats.latency}ms</span></div>
        </div>
      </div>

      {/* Debug Info */}
      {debugInfo && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-3">Debug Information</h3>
          <div className="text-sm space-y-2">
            <div>Total Connections: <span className="font-mono">{debugInfo.summary.totalConnections}</span></div>
            <div>Active Connections: <span className="font-mono">{debugInfo.summary.activeConnections}</span></div>
            <div>Average Latency: <span className="font-mono">{debugInfo.summary.averageLatency.toFixed(2)}ms</span></div>
            <div>Total Errors: <span className="font-mono">{debugInfo.summary.totalErrors}</span></div>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="mb-6 space-y-4">
        <div className="flex gap-2">
          <button
            onClick={handleForceConnect}
            disabled={isConnected}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Force Connect
          </button>
          <button
            onClick={handleForceDisconnect}
            disabled={!isConnected}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Force Disconnect
          </button>
        </div>

        {/* Test Message */}
        <div className="flex gap-2">
          <input
            type="text"
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            placeholder="Enter test message..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            onKeyDown={(e) => e.key === 'Enter' && handleSendTest()}
          />
          <button
            onClick={handleSendTest}
            disabled={!isConnected || !testMessage.trim()}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Send Test
          </button>
        </div>
      </div>

      {/* WebSocket URL */}
      <div className="text-xs text-gray-500 bg-gray-100 p-2 rounded font-mono">
        WebSocket URL: {process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'}
      </div>
    </div>
  )
}

export default WebSocketTest
