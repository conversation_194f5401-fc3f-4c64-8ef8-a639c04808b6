'use client';
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  VStack,
  HS<PERSON>ck,
  <PERSON>,
  Badge,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  SimpleGrid,
  <PERSON>ner,
  Icon,
  Separator,
  Progress
} from '@chakra-ui/react';
import { FaWifi, FaEx<PERSON><PERSON><PERSON>gle, FaRecycle } from 'react-icons/fa';
import { MdSignalWifiOff } from 'react-icons/md';

interface WebSocketMetrics {
  timestamp: string;
  uptime: number;
  connections: {
    totalConnections: number;
    activeConnections: number;
    inactiveConnections: number;
    totalSubscriptions: number;
    averageConnectionTime: number;
    reconnectAttempts: number;
  };
  distribution: {
    userConnections: Record<string, number>;
    productSubscriptions: Record<string, number>;
    connectionsByAge: {
      under1min: number;
      under5min: number;
      under15min: number;
      under1hour: number;
      over1hour: number;
    };
  };
  performance: {
    memoryUsage: {
      rss: number;
      heapTotal: number;
      heapUsed: number;
      external: number;
    };
    cpuUsage: {
      user: number;
      system: number;
    };
    eventLoopDelay: number;
  };
  health: {
    status: string;
    lastHeartbeat: string;
    heartbeatInterval: string;
    maxReconnectAttempts: number;
  };
}

const WebSocketMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<WebSocketMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchMetrics = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/health/websocket', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'get-metrics' })
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch metrics: ${response.status}`);
      }

      const data = await response.json();

      if (data.status === 'success') {
        setMetrics(data.metrics);
        setLastUpdate(new Date());
      } else {
        throw new Error(data.message || 'Failed to fetch metrics');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchMetrics, 10000); // Refresh every 10 seconds
    return () => clearInterval(interval);
  }, [autoRefresh]);

  const formatBytes = (bytes: number) => {
    return `${Math.round(bytes / 1024 / 1024)} MB`;
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  if (isLoading && !metrics) {
    return (
      <Card.Root>
        <CardBody>
          <HStack justify="center" py={8}>
            <Spinner size="lg" />
            <Text>Loading WebSocket metrics...</Text>
          </HStack>
        </CardBody>
      </Card.Root>
    );
  }

  if (error) {
    return (
      <Card.Root>
        <CardBody>
          <VStack gap={4} py={8}>
            <Icon as={FaExclamationTriangle} color="red.500" boxSize={8} />
            <Text color="red.500" textAlign="center">
              Failed to load WebSocket metrics: {error}
            </Text>
            <Button onClick={fetchMetrics} colorScheme="red" variant="outline">
              <Icon as={FaRecycle} mr={2} />
              Retry
            </Button>
          </VStack>
        </CardBody>
      </Card.Root>
    );
  }

  if (!metrics) {
    return (
      <Card.Root>
        <CardBody>
          <Text textAlign="center" py={8}>No metrics available</Text>
        </CardBody>
      </Card.Root>
    );
  }

  const isHealthy = metrics.health.status === 'active';
  const memoryUsagePercent = (metrics.performance.memoryUsage.heapUsed / metrics.performance.memoryUsage.heapTotal) * 100;

  return (
    <VStack gap={6} align="stretch">
      {/* Header */}
      <Card.Root>
        <CardHeader>
          <HStack justify="space-between" align="center">
            <HStack>
              <Icon as={isHealthy ? FaWifi : MdSignalWifiOff} color={isHealthy ? 'green.500' : 'red.500'} />
              <Heading size="lg">WebSocket Monitor</Heading>
              <Badge colorScheme={isHealthy ? 'green' : 'red'} variant="subtle">
                {metrics.health.status}
              </Badge>
            </HStack>
            <HStack>
              <Button
                size="sm"
                variant={autoRefresh ? 'solid' : 'outline'}
                colorScheme="blue"
                onClick={() => setAutoRefresh(!autoRefresh)}
              >
                Auto Refresh {autoRefresh ? 'ON' : 'OFF'}
              </Button>
              <Button size="sm" onClick={fetchMetrics}>
                <Icon as={FaRecycle} mr={2} />
                Refresh
              </Button>
            </HStack>
          </HStack>
          {lastUpdate && (
            <Text fontSize="sm" color="gray.500">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </Text>
          )}
        </CardHeader>
      </Card.Root>

      {/* Connection Stats */}
      <Card.Root>
        <CardHeader>
          <Heading size="md">Connection Statistics</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 2, md: 4 }} gap={4}>
            <Box textAlign="center" p={4} bg="blue.50" borderRadius="md">
              <Text fontSize="sm" color="gray.600" mb={1}>Total Connections</Text>
              <Text fontSize="2xl" fontWeight="bold" color="blue.600">
                {metrics.connections.totalConnections}
              </Text>
            </Box>
            <Box textAlign="center" p={4} bg="green.50" borderRadius="md">
              <Text fontSize="sm" color="gray.600" mb={1}>Active Connections</Text>
              <Text fontSize="2xl" fontWeight="bold" color="green.600">
                {metrics.connections.activeConnections}
              </Text>
            </Box>
            <Box textAlign="center" p={4} bg="purple.50" borderRadius="md">
              <Text fontSize="sm" color="gray.600" mb={1}>Total Subscriptions</Text>
              <Text fontSize="2xl" fontWeight="bold" color="purple.600">
                {metrics.connections.totalSubscriptions}
              </Text>
            </Box>
            <Box textAlign="center" p={4} bg="orange.50" borderRadius="md">
              <Text fontSize="sm" color="gray.600" mb={1}>Avg Connection Time</Text>
              <Text fontSize="2xl" fontWeight="bold" color="orange.600">
                {Math.round(metrics.connections.averageConnectionTime)}s
              </Text>
            </Box>
          </SimpleGrid>
        </CardBody>
      </Card.Root>

      {/* Performance Metrics */}
      <Card.Root>
        <CardHeader>
          <Heading size="md">Performance</Heading>
        </CardHeader>
        <CardBody>
          <VStack gap={4} align="stretch">
            <Box>
              <HStack justify="space-between" mb={2}>
                <Text fontSize="sm" fontWeight="medium">Memory Usage</Text>
                <Text fontSize="sm" color="gray.600">
                  {formatBytes(metrics.performance.memoryUsage.heapUsed)} / {formatBytes(metrics.performance.memoryUsage.heapTotal)}
                </Text>
              </HStack>
              <Progress.Root value={memoryUsagePercent} colorScheme={memoryUsagePercent > 80 ? 'red' : 'blue'}>
                <Progress.Track>
                  <Progress.Range />
                </Progress.Track>
              </Progress.Root>
            </Box>

            <SimpleGrid columns={{ base: 1, md: 3 }} gap={4}>
              <Box>
                <Text fontSize="sm" color="gray.600">Uptime</Text>
                <Text fontSize="lg" fontWeight="semibold">{formatUptime(metrics.uptime)}</Text>
              </Box>
              <Box>
                <Text fontSize="sm" color="gray.600">Event Loop Delay</Text>
                <Text fontSize="lg" fontWeight="semibold">{metrics.performance.eventLoopDelay.toFixed(2)}ms</Text>
              </Box>
              <Box>
                <Text fontSize="sm" color="gray.600">Reconnect Attempts</Text>
                <Text fontSize="lg" fontWeight="semibold">{metrics.connections.reconnectAttempts}</Text>
              </Box>
            </SimpleGrid>
          </VStack>
        </CardBody>
      </Card.Root>

      {/* Connection Distribution */}
      <Card.Root>
        <CardHeader>
          <Heading size="md">Connection Distribution</Heading>
        </CardHeader>
        <CardBody>
          <VStack gap={4} align="stretch">
            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2}>Connections by Age</Text>
              <SimpleGrid columns={{ base: 2, md: 5 }} gap={2}>
                <Box textAlign="center" p={2} bg="gray.50" borderRadius="md">
                  <Text fontSize="xs" color="gray.600">{"< 1min"}</Text>
                  <Text fontSize="lg" fontWeight="bold">{metrics.distribution.connectionsByAge.under1min}</Text>
                </Box>
                <Box textAlign="center" p={2} bg="gray.50" borderRadius="md">
                  <Text fontSize="xs" color="gray.600">{"< 5min"}</Text>
                  <Text fontSize="lg" fontWeight="bold">{metrics.distribution.connectionsByAge.under5min}</Text>
                </Box>
                <Box textAlign="center" p={2} bg="gray.50" borderRadius="md">
                  <Text fontSize="xs" color="gray.600">{"< 15min"}</Text>
                  <Text fontSize="lg" fontWeight="bold">{metrics.distribution.connectionsByAge.under15min}</Text>
                </Box>
                <Box textAlign="center" p={2} bg="gray.50" borderRadius="md">
                  <Text fontSize="xs" color="gray.600">{"< 1hour"}</Text>
                  <Text fontSize="lg" fontWeight="bold">{metrics.distribution.connectionsByAge.under1hour}</Text>
                </Box>
                <Box textAlign="center" p={2} bg="gray.50" borderRadius="md">
                  <Text fontSize="xs" color="gray.600">{"> 1hour"}</Text>
                  <Text fontSize="lg" fontWeight="bold">{metrics.distribution.connectionsByAge.over1hour}</Text>
                </Box>
              </SimpleGrid>
            </Box>

            <Separator />

            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2}>Top Product Subscriptions</Text>
              <VStack gap={2} align="stretch">
                {Object.entries(metrics.distribution.productSubscriptions)
                  .sort(([, a], [, b]) => b - a)
                  .slice(0, 5)
                  .map(([productId, count]) => (
                    <HStack key={productId} justify="space-between" p={2} bg="gray.50" borderRadius="md">
                      <Text fontSize="sm" fontFamily="mono">Product {productId}</Text>
                      <Badge colorScheme="blue">{count} subscribers</Badge>
                    </HStack>
                  ))}
                {Object.keys(metrics.distribution.productSubscriptions).length === 0 && (
                  <Text fontSize="sm" color="gray.500" textAlign="center">No product subscriptions</Text>
                )}
              </VStack>
            </Box>
          </VStack>
        </CardBody>
      </Card.Root>
    </VStack>
  );
};

export default WebSocketMonitor;
