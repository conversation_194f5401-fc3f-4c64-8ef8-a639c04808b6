'use client';

import { useSession } from 'next-auth/react';
import { useState } from 'react';

export default function SessionDebug() {
  const { data: session, status } = useSession();
  const [showDetails, setShowDetails] = useState(false);

  if (status === 'loading') {
    return (
      <div className="fixed bottom-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded shadow-lg">
        <p className="font-bold">Session Status: Loading...</p>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 shadow-lg rounded-lg p-4 max-w-md">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-bold text-sm">Session Debug</h3>
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-xs bg-blue-500 text-white px-2 py-1 rounded"
        >
          {showDetails ? 'Hide' : 'Show'} Details
        </button>
      </div>
      
      <div className="space-y-2 text-xs">
        <div>
          <span className="font-semibold">Status:</span>{' '}
          <span className={`px-2 py-1 rounded ${
            status === 'authenticated' ? 'bg-green-100 text-green-800' :
            status === 'unauthenticated' ? 'bg-red-100 text-red-800' :
            'bg-yellow-100 text-yellow-800'
          }`}>
            {status}
          </span>
        </div>
        
        {session?.user && (
          <div>
            <span className="font-semibold">User:</span> {session.user.name || 'No name'}
          </div>
        )}
        
        {session?.user?.email && (
          <div>
            <span className="font-semibold">Email:</span> {session.user.email}
          </div>
        )}
        
        {(session as any)?.accessToken && (
          <div>
            <span className="font-semibold">Access Token:</span>{' '}
            <span className="text-green-600">✓ Present</span>
          </div>
        )}
        
        {showDetails && (
          <div className="mt-3 p-2 bg-gray-50 rounded">
            <p className="font-semibold mb-1">Full Session Data:</p>
            <pre className="text-xs overflow-auto max-h-40">
              {JSON.stringify(session, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
