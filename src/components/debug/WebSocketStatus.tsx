'use client'

import React from 'react'
import { Box, Badge, Text, VStack, HStack, Button } from '@chakra-ui/react'
import { useWebSocketContext } from '@/contexts/WebSocketContext'

interface WebSocketStatusProps {
  showDetails?: boolean
  position?: 'fixed' | 'relative'
  showReconnectButton?: boolean
}

const WebSocketStatus: React.FC<WebSocketStatusProps> = ({
  showDetails = false,
  position = 'relative',
  showReconnectButton = false
}) => {
  const context = useWebSocketContext()

  if (!context) {
    return (
      <Box 
        position={position}
        top={position === 'fixed' ? 4 : undefined}
        right={position === 'fixed' ? 4 : undefined}
        p={2}
        bg="red.50"
        border="1px solid"
        borderColor="red.200"
        borderRadius="md"
        zIndex={1000}
      >
        <Badge colorScheme="red">WebSocket: Not Available</Badge>
      </Box>
    )
  }

  const { isConnected, connectionStatus, connectionStats, reconnect } = context

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'green'
      case 'connecting': return 'yellow'
      case 'error': return 'red'
      case 'disconnected': return 'gray'
      default: return 'gray'
    }
  }

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Connected'
      case 'connecting': return 'Connecting...'
      case 'error': return 'Error'
      case 'disconnected': return 'Disconnected'
      default: return 'Unknown'
    }
  }

  return (
    <Box 
      position={position}
      top={position === 'fixed' ? 4 : undefined}
      right={position === 'fixed' ? 4 : undefined}
      p={2}
      bg={`${getStatusColor()}.50`}
      border="1px solid"
      borderColor={`${getStatusColor()}.200`}
      borderRadius="md"
      zIndex={1000}
      minW="200px"
    >
      <VStack align="stretch" gap={1}>
        <HStack justify="space-between">
          <Text fontSize="sm" fontWeight="medium">WebSocket</Text>
          <Badge colorScheme={getStatusColor()}>
            {getStatusText()}
          </Badge>
        </HStack>
        
        {showDetails && (
          <VStack align="stretch" gap={1} fontSize="xs" color="gray.600">
            <HStack justify="space-between">
              <Text>Messages Received:</Text>
              <Text>{connectionStats.messagesReceived}</Text>
            </HStack>
            <HStack justify="space-between">
              <Text>Messages Sent:</Text>
              <Text>{connectionStats.messagesSent}</Text>
            </HStack>
            <HStack justify="space-between">
              <Text>Reconnect Attempts:</Text>
              <Text>{connectionStats.reconnectAttempts}</Text>
            </HStack>
            <HStack justify="space-between">
              <Text>Uptime:</Text>
              <Text>{Math.floor(connectionStats.uptime / 1000)}s</Text>
            </HStack>
          </VStack>
        )}

        {showReconnectButton && (connectionStatus === 'error' || connectionStatus === 'disconnected') && (
          <Button
            size="xs"
            colorScheme="blue"
            onClick={reconnect}
          >
            Reconnect
          </Button>
        )}
      </VStack>
    </Box>
  )
}

export default WebSocketStatus
