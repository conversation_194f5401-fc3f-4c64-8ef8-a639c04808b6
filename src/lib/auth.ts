import type { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import axios from "axios";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  accessToken: string;
  refreshToken: string;
}

interface APIResponse {
  status: boolean;
  message: string;
  data: {
    accessToken: string;
    refreshToken: string;
    user: User;
    expiresAt: number;
  };
}

interface RefreshTokenResponse {
  status: boolean;
  message: string;
  data: {
    accessToken: string;
    refreshToken: string;
    expiresAt: number;
  };
}

async function fetchUserProfile(accessToken: string) {
  try {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/auth/profile`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });

    if (response.data.status) {
      return response.data.data;
    }
    return null;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    return null;
  }
}

async function refreshAccessToken(refreshToken: string) {
  try {
    const response = await axios.post<RefreshTokenResponse>(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/refresh-token`,
      { refreshToken },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 seconds timeout
      }
    );

    if (response.data.status && response.data.data) {
      return {
        accessToken: response.data.data.accessToken,
        refreshToken: response.data.data.refreshToken,
        expiresAt: response.data.data.expiresAt,
      };
    }

    throw new Error(response.data.message || 'Failed to refresh token');
  } catch (error) {
    console.error('Token refresh failed:', error);
    throw error;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
          scope: "openid email profile"
        }
      },
      profile(profile) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture,
          firstName: profile.given_name || '',
          lastName: profile.family_name || '',
          phoneNumber: '',
          accessToken: '', // Will be set during sign-in callback
          refreshToken: '', // Will be set during sign-in callback
        }
      }
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        try {
          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/login`,
            {
              email: credentials.email,
              password: credentials.password,
            }
          );

          if (!data.status || !data.data) {
            throw new Error(data.message || "Invalid credentials");
          }

          return {
            id: data.data.user.id,
            name: `${data.data.user.firstName} ${data.data.user.lastName}`,
            email: data.data.user.email,
            firstName: data.data.user.firstName,
            lastName: data.data.user.lastName,
            phoneNumber: data.data.user.phoneNumber,
            accessToken: data.data.accessToken,
            refreshToken: data.data.refreshToken,
          };
        } catch (error) {
          if (axios.isAxiosError(error)) {
            const message = error.response?.data?.message || "Authentication failed";
            throw new Error(message);
          }
          throw new Error("Authentication failed");
        }
      },
    }),
  ],
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours (reduced for security)
    updateAge: 60 * 60, // Update session every hour
  },
  secret: process.env.NEXTAUTH_SECRET,
  useSecureCookies: false, // Disable secure cookies for HTTP deployment
  cookies: {
    sessionToken: {
      name: "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: false, // Set to false for HTTP deployment
        maxAge: 24 * 60 * 60, // 24 hours
      },
    },
    callbackUrl: {
      name: "next-auth.callback-url",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: false, // Set to false for HTTP deployment
      },
    },
    csrfToken: {
      name: "next-auth.csrf-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: false, // Set to false for HTTP deployment
      },
    },
  },
  callbacks: {
    async jwt({ token, user, account, trigger }) {
      console.log("JWT Callback - trigger:", trigger, "user:", !!user, "account:", !!account);

      // Initial sign in
      if (user && account) {
        if (account.provider === "google") {
          try {
            const { data } = await axios.post<APIResponse>(
              `${process.env.NEXT_PUBLIC_API_URL}/auth/google`,
              {
                googleToken: account.id_token,
              },
              {
                timeout: 10000, // 10 second timeout
                headers: {
                  'Content-Type': 'application/json',
                  'User-Agent': 'NextAuth-Client/1.0'
                }
              }
            );

            if (data.status) {
              return {
                ...token,
                id: data.data.user.id,
                email: data.data.user.email,
                firstName: data.data.user.firstName,
                lastName: data.data.user.lastName,
                phoneNumber: data.data.user.phoneNumber,
                accessToken: data.data.accessToken,
                refreshToken: data.data.refreshToken,
                expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24 hours (reduced for security)
                tokenVersion: 1, // Add token versioning for security
              };
            }
          } catch (error) {
            console.error("Google auth failed:", error);
            return token;
          }
        } else {
          // Credentials provider
          return {
            ...token,
            id: (user as any).id,
            email: (user as any).email,
            firstName: (user as any).firstName,
            lastName: (user as any).lastName,
            phoneNumber: (user as any).phoneNumber,
            accessToken: (user as any).accessToken,
            refreshToken: (user as any).refreshToken,
            expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24 hours (reduced for security)
            tokenVersion: 1, // Add token versioning
          };
        }
      }

      // Check if token is expired or needs refresh (10 minutes before expiry)
      if (token.expiresAt && Date.now() > (token.expiresAt as number) - 10 * 60 * 1000) {
        if (token.refreshToken) {
          try {
            console.log("Refreshing access token...");
            const refreshedTokens = await refreshAccessToken(token.refreshToken as string);

            return {
              ...token,
              accessToken: refreshedTokens.accessToken,
              refreshToken: refreshedTokens.refreshToken,
              expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
              error: undefined, // Clear any previous errors
              tokenVersion: (token.tokenVersion as number || 1) + 1, // Increment version
            };
          } catch (error) {
            console.error("Token refresh failed:", error);

            // Mark token as expired and return with error
            return {
              ...token,
              error: "RefreshAccessTokenError",
              expiresAt: Date.now() - 1000, // Mark as expired
            };
          }
        } else {
          // No refresh token available
          console.warn("No refresh token available for token refresh");
          return {
            ...token,
            error: "NoRefreshToken",
            expiresAt: Date.now() - 1000, // Mark as expired
          };
        }
      }

      // Force refresh on update trigger
      if (trigger === "update" && token.refreshToken) {
        try {
          console.log("Force refreshing token due to update trigger...");
          const refreshedTokens = await refreshAccessToken(token.refreshToken as string);

          return {
            ...token,
            accessToken: refreshedTokens.accessToken,
            refreshToken: refreshedTokens.refreshToken,
            expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
            error: undefined,
            tokenVersion: (token.tokenVersion as number || 1) + 1,
          };
        } catch (error) {
          console.error("Force token refresh failed:", error);
        }
      }

      return token;
    },
    async session({ session, token }) {
      console.log("Session Callback - token keys:", Object.keys(token), "session user:", !!session.user);

      // Handle token errors (expired, invalid, etc.)
      if (token.error) {
        console.error("Session error:", token.error);
        // Clear session data but return session object
        session.user = {} as any;
        return session;
      }

      // Validate token expiry
      if (token.expiresAt && Date.now() > (token.expiresAt as number)) {
        console.warn("Token has expired in session callback");
        // Clear session data but return session object
        session.user = {} as any;
        return session;
      }

      // Ensure we have required token data
      if (!token.accessToken || !token.id) {
        console.warn("Missing required token data");
        // Clear session data but return session object
        session.user = {} as any;
        return session;
      }

      // Set tokens in session
      session.accessToken = token.accessToken as string;
      session.refreshToken = token.refreshToken as string;

      // Use token data directly for better reliability
      session.user = {
        id: token.id as string,
        firstName: token.firstName as string,
        lastName: token.lastName as string,
        email: token.email as string,
        phoneNumber: token.phoneNumber as string,
        name: `${token.firstName} ${token.lastName}`,
      };

      // Attach tokens to session for API calls
      (session as any).accessToken = token.accessToken;
      (session as any).refreshToken = token.refreshToken;
      (session as any).expiresAt = token.expiresAt;

      return session;
    },
  },
};