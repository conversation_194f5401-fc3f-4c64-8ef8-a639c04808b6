'use client'

import { useCallback, useEffect, useRef, useState, useMemo } from 'react'
import { useWebSocketContextRequired } from '@/contexts/WebSocketContext'
import { useQueryClient } from '@tanstack/react-query'

interface ProductWebSocketOptions {
  productId: string
  onBidUpdate?: (data: any) => void
  onAuctionExtended?: (data: any) => void
  onAuctionEnded?: (data: any) => void
  onError?: (error: any) => void
  enableAutoReconnect?: boolean
  maxRetries?: number
  retryDelay?: number
}

interface ProductWebSocketState {
  isSubscribed: boolean
  lastBidUpdate: any | null
  connectionQuality: 'excellent' | 'good' | 'poor' | 'unknown'
  subscriptionHealth: {
    messagesReceived: number
    lastMessageTime: number
    errorCount: number
  }
}

export const useProductWebSocket = (options: ProductWebSocketOptions) => {
  const {
    productId,
    onBidUpdate,
    onAuctionExtended,
    onAuctionEnded,
    onError,
    enableAutoReconnect = true,
    maxRetries = 3,
    retryDelay = 2000
  } = options

  const {
    isConnected,
    connectionStatus,
    subscribeToProduct,
    getConnectionHealth,
    connectionStats
  } = useWebSocketContextRequired()

  const queryClient = useQueryClient()

  // Memoized initial state to prevent re-creation
  const initialState = useMemo<ProductWebSocketState>(() => ({
    isSubscribed: false,
    lastBidUpdate: null,
    connectionQuality: 'unknown',
    subscriptionHealth: {
      messagesReceived: 0,
      lastMessageTime: 0,
      errorCount: 0
    }
  }), [])

  // State management with stable initial state
  const [state, setState] = useState<ProductWebSocketState>(initialState)

  // Stable refs to prevent re-renders
  const retryCountRef = useRef(0)
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const unsubscribeRef = useRef<(() => void) | null>(null)
  const lastHealthCheckRef = useRef<number>(0)
  const isSubscribingRef = useRef(false)
  const lastProductIdRef = useRef<string>('')
  const subscriptionDebounceRef = useRef<NodeJS.Timeout | null>(null)

  // Refs for callbacks to prevent dependency changes
  const onBidUpdateRef = useRef(onBidUpdate)
  const onAuctionExtendedRef = useRef(onAuctionExtended)
  const onAuctionEndedRef = useRef(onAuctionEnded)
  const onErrorRef = useRef(onError)

  // Update callback refs when props change
  useEffect(() => {
    onBidUpdateRef.current = onBidUpdate
    onAuctionExtendedRef.current = onAuctionExtended
    onAuctionEndedRef.current = onAuctionEnded
    onErrorRef.current = onError
  }, [onBidUpdate, onAuctionExtended, onAuctionEnded, onError])

  // Optimized message handler with stable dependencies
  const handleProductMessage = useCallback((data: any) => {
    try {
      console.log(`📨 Product WebSocket message for ${lastProductIdRef.current}:`, data)

      // Batch state updates to prevent multiple re-renders
      setState(prev => {
        const newState = {
          ...prev,
          subscriptionHealth: {
            ...prev.subscriptionHealth,
            messagesReceived: prev.subscriptionHealth.messagesReceived + 1,
            lastMessageTime: Date.now()
          }
        }

        // Only update lastBidUpdate if it's actually a new bid
        if (data.type === 'new_bid') {
          newState.lastBidUpdate = data
        }

        return newState
      })

      // Handle different message types with debounced query invalidation
      switch (data.type) {
        case 'new_bid':
          console.log(`💰 New bid received for product ${lastProductIdRef.current}:`, data)

          // Debounced query invalidation to prevent excessive API calls
          setTimeout(() => {
            queryClient.invalidateQueries({
              queryKey: ['products', 'detail', lastProductIdRef.current]
            })
            queryClient.invalidateQueries({
              queryKey: ['bidding', 'history', lastProductIdRef.current]
            })
            queryClient.invalidateQueries({
              queryKey: ['bidding', 'user-bid', lastProductIdRef.current]
            })
          }, 100)

          onBidUpdateRef.current?.(data)
          break

        case 'auction_extended':
          console.log(`⏰ Auction extended for product ${lastProductIdRef.current}:`, data)

          setTimeout(() => {
            queryClient.invalidateQueries({
              queryKey: ['products', 'detail', lastProductIdRef.current]
            })
            queryClient.invalidateQueries({
              queryKey: ['auction-extension', 'logs', lastProductIdRef.current]
            })
          }, 100)

          onAuctionExtendedRef.current?.(data)
          break

        case 'auction_ended':
          console.log(`🏁 Auction ended for product ${lastProductIdRef.current}:`, data)

          setTimeout(() => {
            queryClient.invalidateQueries({
              queryKey: ['products', 'detail', lastProductIdRef.current]
            })
            queryClient.invalidateQueries({
              queryKey: ['bidding', 'history', lastProductIdRef.current]
            })
          }, 100)

          onAuctionEndedRef.current?.(data)
          break

        default:
          console.log(`📋 Other message type for product ${lastProductIdRef.current}:`, data.type)
      }

      // Reset retry count on successful message
      retryCountRef.current = 0

    } catch (error) {
      console.error(`❌ Error handling product WebSocket message:`, error)

      setState(prev => ({
        ...prev,
        subscriptionHealth: {
          ...prev.subscriptionHealth,
          errorCount: prev.subscriptionHealth.errorCount + 1
        }
      }))

      onErrorRef.current?.(error)
    }
  }, [queryClient]) // Remove callback dependencies since we use refs

  // Optimized subscription with stable dependencies and duplicate prevention
  const subscribe = useCallback(() => {
    // Prevent duplicate subscriptions
    if (isSubscribingRef.current) {
      console.log(`⏳ Already subscribing to product ${productId}, skipping`)
      return
    }

    if (!productId || !isConnected) {
      console.log(`⏳ Cannot subscribe to product ${productId}: not connected or no productId`)
      return
    }

    // Check if already subscribed to the same product
    if (lastProductIdRef.current === productId && state.isSubscribed) {
      console.log(`✅ Already subscribed to product ${productId}`)
      return
    }

    isSubscribingRef.current = true

    try {
      console.log(`🔔 Subscribing to product: ${productId}`)

      // Clean up existing subscription if different product
      if (unsubscribeRef.current && lastProductIdRef.current !== productId) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }

      // Update product ID reference
      lastProductIdRef.current = productId

      // Create new subscription
      const unsubscribe = subscribeToProduct(productId, handleProductMessage)
      unsubscribeRef.current = unsubscribe

      setState(prev => ({
        ...prev,
        isSubscribed: true
      }))

      console.log(`✅ Successfully subscribed to product: ${productId}`)

    } catch (error) {
      console.error(`❌ Failed to subscribe to product ${productId}:`, error)

      setState(prev => ({
        ...prev,
        isSubscribed: false,
        subscriptionHealth: {
          ...prev.subscriptionHealth,
          errorCount: prev.subscriptionHealth.errorCount + 1
        }
      }))

      // Retry logic with exponential backoff
      if (enableAutoReconnect && retryCountRef.current < maxRetries) {
        retryCountRef.current++
        const delay = retryDelay * Math.pow(2, retryCountRef.current - 1)
        console.log(`🔄 Retrying subscription for product ${productId} (attempt ${retryCountRef.current}/${maxRetries}) in ${delay}ms`)

        retryTimeoutRef.current = setTimeout(() => {
          isSubscribingRef.current = false
          subscribe()
        }, delay)
      } else {
        isSubscribingRef.current = false
      }

      onErrorRef.current?.(error)
    } finally {
      if (retryCountRef.current === 0) {
        isSubscribingRef.current = false
      }
    }
  }, [productId, isConnected, subscribeToProduct, handleProductMessage, enableAutoReconnect, maxRetries, retryDelay]) // Remove onError and state.isSubscribed to prevent loops

  // Optimized cleanup function
  const cleanup = useCallback(() => {
    console.log(`🧹 Cleaning up product WebSocket subscription for: ${lastProductIdRef.current}`)

    // Clear subscription debounce timeout
    if (subscriptionDebounceRef.current) {
      clearTimeout(subscriptionDebounceRef.current)
      subscriptionDebounceRef.current = null
    }

    // Clear retry timeout
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current)
      retryTimeoutRef.current = null
    }

    // Reset subscribing flag
    isSubscribingRef.current = false

    // Unsubscribe from product updates
    if (unsubscribeRef.current) {
      unsubscribeRef.current()
      unsubscribeRef.current = null
    }

    setState(prev => ({
      ...prev,
      isSubscribed: false
    }))

    // Clear product ID reference
    lastProductIdRef.current = ''
  }, [])

  // Optimized connection health monitoring with reduced frequency
  useEffect(() => {
    const healthCheckInterval = setInterval(() => {
      const now = Date.now()

      // Skip if no recent activity to reduce unnecessary processing
      if (now - lastHealthCheckRef.current < 25000) {
        return
      }

      const timeSinceLastMessage = now - state.subscriptionHealth.lastMessageTime
      const connectionHealth = getConnectionHealth()

      // Update connection quality based on multiple factors
      let quality: 'excellent' | 'good' | 'poor' | 'unknown' = 'unknown'

      if (isConnected && state.isSubscribed) {
        if (connectionHealth.isHealthy && timeSinceLastMessage < 60000) {
          quality = 'excellent'
        } else if (connectionHealth.isHealthy && timeSinceLastMessage < 120000) {
          quality = 'good'
        } else if (timeSinceLastMessage < 300000) {
          quality = 'poor'
        }
      }

      // Only update state if quality actually changed
      setState(prev => {
        if (prev.connectionQuality !== quality) {
          return { ...prev, connectionQuality: quality }
        }
        return prev
      })

      // Auto-reconnect if connection is poor and conditions are met
      if (quality === 'poor' && enableAutoReconnect && isConnected && !state.isSubscribed && !isSubscribingRef.current) {
        console.log(`🔄 Poor connection quality detected, attempting to resubscribe to product ${lastProductIdRef.current}`)
        subscribe()
      }

      lastHealthCheckRef.current = now
    }, 30000) // Check every 30 seconds

    return () => clearInterval(healthCheckInterval)
  }, [getConnectionHealth, state.subscriptionHealth.lastMessageTime, state.isSubscribed, state.connectionQuality, enableAutoReconnect, isConnected, subscribe])

  // Optimized subscription effect with productId change detection and debouncing
  useEffect(() => {
    // Clear any existing debounce timeout
    if (subscriptionDebounceRef.current) {
      clearTimeout(subscriptionDebounceRef.current)
      subscriptionDebounceRef.current = null
    }

    // Update product ID reference
    if (productId && productId !== lastProductIdRef.current) {
      lastProductIdRef.current = productId
    }

    // Debounce subscription changes to prevent rapid subscribe/unsubscribe cycles
    subscriptionDebounceRef.current = setTimeout(() => {
      if (isConnected && productId) {
        subscribe()
      } else {
        cleanup()
      }
    }, 300) // 300ms debounce

    return () => {
      if (subscriptionDebounceRef.current) {
        clearTimeout(subscriptionDebounceRef.current)
        subscriptionDebounceRef.current = null
      }
      cleanup()
    }
  }, [isConnected, productId, subscribe, cleanup])

  // Cleanup on unmount
  useEffect(() => {
    return cleanup
  }, [cleanup])

  return {
    ...state,
    isConnected,
    connectionStatus,
    connectionQuality: connectionStats.connectionQuality,
    resubscribe: subscribe,
    cleanup
  }
}
