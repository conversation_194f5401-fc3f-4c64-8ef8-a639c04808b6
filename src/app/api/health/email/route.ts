import { NextResponse } from 'next/server';
import emailService from '../../../../../server/services/email.service';

export async function GET() {
  try {
    // Check if email service is configured and ready
    const isReady = emailService.isReady();
    
    if (!isReady) {
      return NextResponse.json({
        status: 'unhealthy',
        service: 'email',
        configured: false,
        message: 'Email service not configured - missing SMTP credentials',
        timestamp: new Date().toISOString(),
      }, { status: 503 });
    }

    // Basic configuration check
    const config = {
      host: process.env.SMTP_HOST || 'not configured',
      port: process.env.SMTP_PORT || 'not configured',
      user: process.env.SMTP_USER ? 'configured' : 'not configured',
      pass: process.env.SMTP_PASS ? 'configured' : 'not configured',
    };

    return NextResponse.json({
      status: 'healthy',
      service: 'email',
      configured: true,
      config: config,
      timestamp: new Date().toISOString(),
    }, { status: 200 });

  } catch (error) {
    console.error('Email health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      service: 'email',
      error: error instanceof Error ? error.message : 'Unknown email service error',
      timestamp: new Date().toISOString(),
    }, { status: 503 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json({
        status: 'error',
        message: 'Email address is required for testing',
        timestamp: new Date().toISOString(),
      }, { status: 400 });
    }

    // Check if email service is ready
    if (!emailService.isReady()) {
      return NextResponse.json({
        status: 'error',
        message: 'Email service not configured',
        timestamp: new Date().toISOString(),
      }, { status: 503 });
    }

    // Send test email
    const result = await emailService.sendTestEmail(email);
    
    if (result.success) {
      return NextResponse.json({
        status: 'success',
        message: 'Test email sent successfully',
        data: result.data,
        timestamp: new Date().toISOString(),
      }, { status: 200 });
    } else {
      return NextResponse.json({
        status: 'error',
        message: result.message,
        error: result.error,
        timestamp: new Date().toISOString(),
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Email test failed:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Failed to send test email',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
