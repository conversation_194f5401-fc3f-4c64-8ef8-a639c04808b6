import { NextResponse } from 'next/server';
import { PrismaClient } from '../../../../../generated/client';

export async function GET() {
  try {
    // This endpoint specifically checks database connectivity
    // Only available at runtime, not during build

    if (process.env.NODE_ENV !== 'production') {
      return NextResponse.json({
        status: 'skipped',
        message: 'Database health check only available in production',
        timestamp: new Date().toISOString(),
      }, { status: 200 });
    }
    
    const prisma = new PrismaClient();

    try {
      // Test database connection
      await prisma.$queryRaw`SELECT 1 as test`;
      
      // Get some basic database info
      const result = await prisma.$queryRaw`SELECT VERSION() as version` as any[];
      const dbVersion = result[0]?.version || 'unknown';

      await prisma.$disconnect();

      return NextResponse.json({
        status: 'healthy',
        database: 'connected',
        version: dbVersion,
        timestamp: new Date().toISOString(),
      }, { status: 200 });

    } catch (dbError) {
      await prisma.$disconnect();
      throw dbError;
    }

  } catch (error) {
    console.error('Database health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      database: 'disconnected',
      error: error instanceof Error ? error.message : 'Unknown database error',
      timestamp: new Date().toISOString(),
    }, { status: 503 });
  }
}
