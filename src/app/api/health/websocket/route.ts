import { NextResponse } from 'next/server';
import WebSocket from 'ws';

export async function GET(): Promise<Response> {
  try {
    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001';
    const wsHealthUrl = wsUrl.replace('ws://', 'http://').replace('wss://', 'https://') + '/health';
    
    // Try to fetch WebSocket health status
    const response = await fetch(wsHealthUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add timeout
      signal: AbortSignal.timeout(5000)
    });

    if (!response.ok) {
      throw new Error(`WebSocket health check failed with status: ${response.status}`);
    }

    const wsHealthData = await response.json();

    return NextResponse.json({
      status: 'healthy',
      service: 'websocket-proxy',
      websocket: wsHealthData,
      timestamp: new Date().toISOString(),
      config: {
        wsUrl: process.env.NEXT_PUBLIC_WS_URL,
        wsPort: process.env.WS_PORT || '3001'
      }
    }, { status: 200 });

  } catch (error) {
    console.error('WebSocket health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      service: 'websocket-proxy',
      error: error instanceof Error ? error.message : 'Unknown WebSocket error',
      timestamp: new Date().toISOString(),
      config: {
        wsUrl: process.env.NEXT_PUBLIC_WS_URL,
        wsPort: process.env.WS_PORT || '3001'
      }
    }, { status: 503 });
  }
}

export async function POST(request: Request): Promise<Response> {
  try {
    const body = await request.json();
    const { action } = body;

    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001';
    
    if (action === 'test-connection') {
      // Test WebSocket connection
      return new Promise<Response>((resolve) => {
        const ws = new WebSocket(wsUrl);
        const timeout = setTimeout(() => {
          ws.close();
          resolve(NextResponse.json({
            status: 'error',
            message: 'WebSocket connection timeout',
            timestamp: new Date().toISOString()
          }, { status: 408 }));
        }, 5000);

        ws.on('open', () => {
          clearTimeout(timeout);
          ws.close();
          resolve(NextResponse.json({
            status: 'success',
            message: 'WebSocket connection test successful',
            timestamp: new Date().toISOString()
          }, { status: 200 }));
        });

        ws.on('error', (error: Error) => {
          clearTimeout(timeout);
          resolve(NextResponse.json({
            status: 'error',
            message: 'WebSocket connection test failed',
            error: error.message,
            timestamp: new Date().toISOString()
          }, { status: 500 }));
        });
      });
    }

    if (action === 'get-metrics') {
      // Get detailed WebSocket metrics
      const wsHealthUrl = wsUrl.replace('ws://', 'http://').replace('wss://', 'https://') + '/metrics';
      
      const response = await fetch(wsHealthUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(5000)
      });

      if (!response.ok) {
        throw new Error(`WebSocket metrics fetch failed with status: ${response.status}`);
      }

      const metrics = await response.json();

      return NextResponse.json({
        status: 'success',
        metrics,
        timestamp: new Date().toISOString()
      }, { status: 200 });
    }

    return NextResponse.json({
      status: 'error',
      message: 'Invalid action. Supported actions: test-connection, get-metrics',
      timestamp: new Date().toISOString()
    }, { status: 400 });

  } catch (error) {
    console.error('WebSocket action failed:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'WebSocket action failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
