import React from 'react';
import { Box, Container, Heading, Text, VStack } from '@chakra-ui/react';
import WebSocketMonitor from '@/components/admin/WebSocketMonitor';

export default function WebSocketAdminPage() {
  return (
    <Container maxW="7xl" py={8}>
      <VStack gap={6} align="stretch">
        <Box>
          <Heading size="xl" mb={2}>WebSocket Administration</Heading>
          <Text color="gray.600">
            Monitor WebSocket connections, performance metrics, and health status in real-time.
          </Text>
        </Box>
        
        <WebSocketMonitor />
      </VStack>
    </Container>
  );
}
