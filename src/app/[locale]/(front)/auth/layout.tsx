import {
    Flex,
    Stack,
    Image,
} from '@chakra-ui/react'
import Link from 'next/link';

export default async function layout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <Flex
            minH={'100vh'}
            justify={'center'}>
            <Stack
                borderWidth={1}
                borderColor={'gray.200'}
                w={{
                    base: 'full',
                    md: '6/12',
                    lg: '5/12',
                    xl: "4/12",
                }}
                bg="white"
                mx={'auto'}
                p={8}>
                <Link href={"/"}>
                    <Image
                        src="/logo.png"
                        alt="Logo"
                        width={72}
                        height={16}
                        objectFit="cover"
                    />
                </Link>
                {children}
            </Stack >
        </Flex >

    )
}