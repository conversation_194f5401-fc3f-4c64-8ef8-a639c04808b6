'use client';

import { Suspense } from 'react';
import { Box, Spinner, VStack, Text } from '@chakra-ui/react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

// Import our new components
import { CheckoutTypeDetector, useCheckoutParams } from '@/components/checkout/CheckoutTypeDetector';
import { CheckoutValidationWrapper } from '@/components/checkout/CheckoutValidationWrapper';
import { CartCheckout } from '@/components/checkout/CartCheckout';
import { BuyNowCheckout } from '@/components/checkout/BuyNowCheckout';
import { BiddingCheckout } from '@/components/checkout/BiddingCheckout';

// Loading component
const CheckoutLoading = () => (
  <Box textAlign="center" py={10}>
    <VStack gap={4}>
      <Spinner size="lg" color="blue.500" />
      <Text>Loading checkout...</Text>
    </VStack>
  </Box>
);

// Main checkout content component
const CheckoutContent = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const checkoutParams = useCheckoutParams();

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/checkout');
    }
  }, [status, router]);

  if (status === 'loading') {
    return <CheckoutLoading />;
  }

  if (!session) {
    return null; // Will redirect
  }

  return (
    <CheckoutValidationWrapper
      checkoutType={checkoutParams.type}
      productId={checkoutParams.productId}
      bidId={checkoutParams.bidId}
    >
      {(validationData) => {
        // Render appropriate checkout component based on type
        switch (checkoutParams.type) {
          case 'cart':
            return <CartCheckout validationData={validationData} />;

          case 'buy-now':
            return <BuyNowCheckout validationData={validationData} />;

          case 'bidding':
            return <BiddingCheckout validationData={validationData} />;

          default:
            return (
              <Box textAlign="center" py={10}>
                <Text color="red.500">Invalid checkout type</Text>
              </Box>
            );
        }
      }}
    </CheckoutValidationWrapper>
  );
};

// Main page component with Suspense wrapper
export default function CheckoutPage() {
  return (
    <CheckoutTypeDetector>
      <Suspense fallback={<CheckoutLoading />}>
        <CheckoutContent />
      </Suspense>
    </CheckoutTypeDetector>
  );
}