'use client'
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useLocale } from 'next-intl'
import { getCurrencySymbol as getCurrencySymbolUtil } from '@/utils/currency'
import { useCurrencyService } from '@/services/useCurrencyService'

// Types
export type SupportedCurrency = 'USD' | 'IDR'
export type SupportedLanguage = 'en' | 'id'

interface CurrencyLanguageContextType {
  // Current display state (temporary, not saved until save() is called)
  currency: SupportedCurrency
  language: SupportedLanguage

  // Temporary state for settings modal/dropdown
  tempCurrency: SupportedCurrency
  tempLanguage: SupportedLanguage
  setTempCurrency: (currency: SupportedCurrency) => void
  setTempLanguage: (language: SupportedLanguage) => void

  // Save/Cancel functions
  saveSettings: () => void
  cancelSettings: () => void
  hasUnsavedChanges: boolean

  // Exchange rate state (from backend)
  exchangeRate: number
  isLoadingRate: boolean
  lastUpdated: string | null
  refreshExchangeRate: () => Promise<void>

  // Helper functions
  formatPrice: (amount: number, fromCurrency?: SupportedCurrency) => string
  convertPrice: (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency) => number
  convertAndFormatPrice: (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency) => string
  getCurrencySymbol: () => string
  getLanguageLabel: () => string
  getCurrencyLabel: () => string

  // Status
  isReady: boolean
}

const CurrencyLanguageContext = createContext<CurrencyLanguageContextType | undefined>(undefined)

interface CurrencyLanguageProviderProps {
  children: ReactNode
}

export const CurrencyLanguageProvider: React.FC<CurrencyLanguageProviderProps> = ({ children }) => {
  const router = useRouter()
  const pathname = usePathname()
  const currentLocale = useLocale() as SupportedLanguage

  // Initialize states
  const [currency, setCurrencyState] = useState<SupportedCurrency>('USD')
  const [language, setLanguageState] = useState<SupportedLanguage>(currentLocale)
  const [tempCurrency, setTempCurrency] = useState<SupportedCurrency>('USD')
  const [tempLanguage, setTempLanguage] = useState<SupportedLanguage>(currentLocale)
  const [isReady, setIsReady] = useState(false)

  // Load saved preferences from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedCurrency = localStorage.getItem('preferred-currency') as SupportedCurrency
      const savedLanguage = localStorage.getItem('preferred-language') as SupportedLanguage

      const finalCurrency = savedCurrency && ['USD', 'IDR'].includes(savedCurrency)
        ? savedCurrency
        : (currentLocale === 'id' ? 'IDR' : 'USD')

      const finalLanguage = savedLanguage && ['en', 'id'].includes(savedLanguage)
        ? savedLanguage
        : currentLocale

      setCurrencyState(finalCurrency)
      setLanguageState(finalLanguage)
      setTempCurrency(finalCurrency)
      setTempLanguage(finalLanguage)
      setIsReady(true)
    }
  }, [currentLocale])

  // Use new currency service for better performance
  const currencyService = useCurrencyService()

  // Check if there are unsaved changes
  const hasUnsavedChanges = tempCurrency !== currency || tempLanguage !== language

  // Save settings function
  const saveSettings = async () => {
    console.log(`🔄 Saving settings: currency ${currency} -> ${tempCurrency}, language ${language} -> ${tempLanguage}`)

    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-currency', tempCurrency)
      localStorage.setItem('preferred-language', tempLanguage)
    }

    // Update actual state
    const languageChanged = tempLanguage !== language
    setCurrencyState(tempCurrency)
    setLanguageState(tempLanguage)

    // Invalidate all currency-related queries to trigger re-fetch
    await currencyService.invalidateCurrencyQueries()

    // Navigate to new language if changed
    if (languageChanged) {
      navigateToLanguage(tempLanguage)
    }

    console.log(`✅ Settings saved: currency ${tempCurrency}, language ${tempLanguage}`)
  }

  // Cancel settings function
  const cancelSettings = () => {
    setTempCurrency(currency)
    setTempLanguage(language)
  }

  // Navigation function for language change (called after save)
  const navigateToLanguage = (newLanguage: SupportedLanguage) => {
    const currentPath = pathname.replace(/^\/[a-z]{2}/, '') || '/'
    router.push(`/${newLanguage}${currentPath}`)
  }

  // Helper functions using currency service
  const formatPrice = (amount: number, fromCurrency?: SupportedCurrency): string => {
    const targetCurrency = fromCurrency || currency
    return currencyService.formatPrice(amount, targetCurrency)
  }

  const convertPrice = (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency): number => {
    const targetCurrency = toCurrency || currency
    if (fromCurrency === targetCurrency) return amount
    return currencyService.convertPrice(amount, fromCurrency, targetCurrency)
  }

  const convertAndFormatPrice = (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency): string => {
    const targetCurrency = toCurrency || currency
    const converted = currencyService.convertPrice(amount, fromCurrency, targetCurrency)
    return currencyService.formatPrice(converted, targetCurrency)
  }



  const getCurrencySymbol = (): string => {
    return getCurrencySymbolUtil(currency)
  }

  const getLanguageLabel = (): string => {
    return language === 'en' ? 'English' : 'Bahasa Indonesia'
  }

  const getCurrencyLabel = (): string => {
    return currency === 'USD' ? 'USD ($)' : 'IDR (Rp)'
  }

  const value: CurrencyLanguageContextType = {
    // Current display state
    currency,
    language,

    // Temporary state for settings
    tempCurrency,
    tempLanguage,
    setTempCurrency,
    setTempLanguage,

    // Save/Cancel functions
    saveSettings,
    cancelSettings,
    hasUnsavedChanges,

    // Exchange rate state from currency service
    exchangeRate: currencyService.exchangeRate,
    isLoadingRate: currencyService.isLoading,
    lastUpdated: currencyService.lastUpdated || null,
    refreshExchangeRate: currencyService.refreshExchangeRate,

    // Helper functions
    formatPrice,
    convertPrice,
    convertAndFormatPrice,
    getCurrencySymbol,
    getLanguageLabel,
    getCurrencyLabel,

    // Status
    isReady: currencyService.isReady,
  }

  return (
    <CurrencyLanguageContext.Provider value={value}>
      {children}
    </CurrencyLanguageContext.Provider>
  )
}

// Custom hook to use the context
export const useCurrencyLanguage = (): CurrencyLanguageContextType => {
  const context = useContext(CurrencyLanguageContext)
  if (context === undefined) {
    throw new Error('useCurrencyLanguage must be used within a CurrencyLanguageProvider')
  }
  return context
}

// Currency options for dropdowns
export const CURRENCY_OPTIONS = [
  {
    value: 'USD' as const,
    label: 'USD ($)',
    symbol: '$',
    name: 'US Dollar',
    flag: '🇺🇸',
  },
  {
    value: 'IDR' as const,
    label: 'IDR (Rp)',
    symbol: 'Rp',
    name: 'Indonesian Rupiah',
    flag: '🇮🇩',
  },
]

// Language options for dropdowns
export const LANGUAGE_OPTIONS = [
  {
    value: 'en' as const,
    label: 'English',
    name: 'English',
    flag: '🇺🇸',
  },
  {
    value: 'id' as const,
    label: 'Bahasa Indonesia',
    name: 'Indonesian',
    flag: '🇮🇩',
  },
]

// Helper function to get currency by language
export const getCurrencyByLanguage = (language: SupportedLanguage): SupportedCurrency => {
  return language === 'id' ? 'IDR' : 'USD'
}

// Helper function to get language by currency
export const getLanguageByCurrency = (currency: SupportedCurrency): SupportedLanguage => {
  return currency === 'IDR' ? 'id' : 'en'
}
