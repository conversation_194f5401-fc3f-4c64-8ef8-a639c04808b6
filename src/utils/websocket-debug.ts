/**
 * WebSocket Debug Utility
 * Provides comprehensive debugging and monitoring tools for WebSocket connections
 */

export interface WebSocketDebugInfo {
  connectionId: string;
  url: string;
  readyState: number;
  readyStateText: string;
  protocol: string;
  extensions: string;
  bufferedAmount: number;
  binaryType: string;
  connectionTime: number;
  lastMessageTime: number;
  messagesSent: number;
  messagesReceived: number;
  errors: Array<{ timestamp: number; error: string; type: string }>;
  latencyHistory: number[];
  averageLatency: number;
  connectionQuality: 'excellent' | 'good' | 'poor' | 'unknown';
}

export interface WebSocketMonitorConfig {
  enableLogging: boolean;
  enableLatencyTracking: boolean;
  enableErrorTracking: boolean;
  maxHistorySize: number;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}

export class WebSocketDebugger {
  private static instance: WebSocketDebugger;
  private connections: Map<string, WebSocketDebugInfo> = new Map();
  private config: WebSocketMonitorConfig = {
    enableLogging: true,
    enableLatencyTracking: true,
    enableErrorTracking: true,
    maxHistorySize: 100,
    logLevel: 'info'
  };

  static getInstance(): WebSocketDebugger {
    if (!WebSocketDebugger.instance) {
      WebSocketDebugger.instance = new WebSocketDebugger();
    }
    return WebSocketDebugger.instance;
  }

  configure(config: Partial<WebSocketMonitorConfig>): void {
    this.config = { ...this.config, ...config };
  }

  registerConnection(ws: WebSocket, connectionId: string, url: string): void {
    const debugInfo: WebSocketDebugInfo = {
      connectionId,
      url,
      readyState: ws.readyState,
      readyStateText: this.getReadyStateText(ws.readyState),
      protocol: ws.protocol,
      extensions: ws.extensions,
      bufferedAmount: ws.bufferedAmount,
      binaryType: ws.binaryType,
      connectionTime: Date.now(),
      lastMessageTime: 0,
      messagesSent: 0,
      messagesReceived: 0,
      errors: [],
      latencyHistory: [],
      averageLatency: 0,
      connectionQuality: 'unknown'
    };

    this.connections.set(connectionId, debugInfo);
    this.log('info', `🔗 WebSocket connection registered: ${connectionId} -> ${url}`);
  }

  updateConnectionState(connectionId: string, ws: WebSocket): void {
    const info = this.connections.get(connectionId);
    if (!info) return;

    info.readyState = ws.readyState;
    info.readyStateText = this.getReadyStateText(ws.readyState);
    info.bufferedAmount = ws.bufferedAmount;
  }

  recordMessageSent(connectionId: string, message: any): void {
    const info = this.connections.get(connectionId);
    if (!info) return;

    info.messagesSent++;
    info.lastMessageTime = Date.now();
    
    this.log('debug', `📤 Message sent on ${connectionId}:`, message);
  }

  recordMessageReceived(connectionId: string, message: any): void {
    const info = this.connections.get(connectionId);
    if (!info) return;

    info.messagesReceived++;
    info.lastMessageTime = Date.now();
    
    this.log('debug', `📥 Message received on ${connectionId}:`, message);
  }

  recordLatency(connectionId: string, latency: number): void {
    if (!this.config.enableLatencyTracking) return;

    const info = this.connections.get(connectionId);
    if (!info) return;

    info.latencyHistory.push(latency);
    
    // Keep only recent latency measurements
    if (info.latencyHistory.length > this.config.maxHistorySize) {
      info.latencyHistory.shift();
    }

    // Calculate average latency
    info.averageLatency = info.latencyHistory.reduce((a, b) => a + b, 0) / info.latencyHistory.length;

    // Update connection quality based on latency
    if (info.averageLatency < 100) {
      info.connectionQuality = 'excellent';
    } else if (info.averageLatency < 300) {
      info.connectionQuality = 'good';
    } else {
      info.connectionQuality = 'poor';
    }

    this.log('debug', `📊 Latency recorded for ${connectionId}: ${latency}ms (avg: ${info.averageLatency.toFixed(2)}ms)`);
  }

  recordError(connectionId: string, error: string, type: string = 'unknown'): void {
    if (!this.config.enableErrorTracking) return;

    const info = this.connections.get(connectionId);
    if (!info) return;

    const errorRecord = {
      timestamp: Date.now(),
      error,
      type
    };

    info.errors.push(errorRecord);
    
    // Keep only recent errors
    if (info.errors.length > this.config.maxHistorySize) {
      info.errors.shift();
    }

    this.log('error', `❌ Error recorded for ${connectionId} [${type}]:`, error);
  }

  unregisterConnection(connectionId: string): void {
    const info = this.connections.get(connectionId);
    if (info) {
      this.log('info', `🔌 WebSocket connection unregistered: ${connectionId}`);
      this.connections.delete(connectionId);
    }
  }

  getConnectionInfo(connectionId: string): WebSocketDebugInfo | undefined {
    return this.connections.get(connectionId);
  }

  getAllConnections(): WebSocketDebugInfo[] {
    return Array.from(this.connections.values());
  }

  getConnectionSummary(): {
    totalConnections: number;
    activeConnections: number;
    totalMessagesSent: number;
    totalMessagesReceived: number;
    totalErrors: number;
    averageLatency: number;
  } {
    const connections = this.getAllConnections();
    const activeConnections = connections.filter(c => c.readyState === WebSocket.OPEN);

    return {
      totalConnections: connections.length,
      activeConnections: activeConnections.length,
      totalMessagesSent: connections.reduce((sum, c) => sum + c.messagesSent, 0),
      totalMessagesReceived: connections.reduce((sum, c) => sum + c.messagesReceived, 0),
      totalErrors: connections.reduce((sum, c) => sum + c.errors.length, 0),
      averageLatency: activeConnections.length > 0 
        ? activeConnections.reduce((sum, c) => sum + c.averageLatency, 0) / activeConnections.length 
        : 0
    };
  }

  exportDebugData(): string {
    const summary = this.getConnectionSummary();
    const connections = this.getAllConnections();

    return JSON.stringify({
      timestamp: new Date().toISOString(),
      summary,
      connections,
      config: this.config
    }, null, 2);
  }

  private getReadyStateText(readyState: number): string {
    switch (readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING';
      case WebSocket.OPEN: return 'OPEN';
      case WebSocket.CLOSING: return 'CLOSING';
      case WebSocket.CLOSED: return 'CLOSED';
      default: return 'UNKNOWN';
    }
  }

  private log(level: string, message: string, data?: any): void {
    if (!this.config.enableLogging) return;

    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this.config.logLevel);
    const messageLevelIndex = levels.indexOf(level);

    if (messageLevelIndex >= currentLevelIndex) {
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] [WS-DEBUG] ${message}`;
      
      if (data !== undefined) {
        console.log(logMessage, data);
      } else {
        console.log(logMessage);
      }
    }
  }
}

// Export singleton instance
export const wsDebugger = WebSocketDebugger.getInstance();

// Development helper functions
export const debugWebSocket = {
  getInfo: (connectionId: string) => wsDebugger.getConnectionInfo(connectionId),
  getSummary: () => wsDebugger.getConnectionSummary(),
  exportData: () => wsDebugger.exportDebugData(),
  configure: (config: Partial<WebSocketMonitorConfig>) => wsDebugger.configure(config)
};

// Make it available globally in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).debugWebSocket = debugWebSocket;
}
