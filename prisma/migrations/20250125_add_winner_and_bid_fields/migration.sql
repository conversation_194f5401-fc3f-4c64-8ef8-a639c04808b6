-- AlterTable
ALTER TABLE `Order` ADD COLUMN `bidId` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `Product` ADD COLUMN `winnerBidId` VARCHAR(191) NULL,
    ADD COLUMN `winnerId` VARCHAR(191) NULL;

-- CreateIndex
CREATE INDEX `Order_bidId_idx` ON `Order`(`bidId`);

-- CreateIndex
CREATE UNIQUE INDEX `Product_winnerBidId_key` ON `Product`(`winnerBidId`);

-- CreateIndex
CREATE INDEX `Product_winnerId_idx` ON `Product`(`winnerId`);

-- CreateIndex
CREATE INDEX `Product_winnerBidId_idx` ON `Product`(`winnerBidId`);

-- AddForeignKey
ALTER TABLE `Product` ADD CONSTRAINT `Product_winnerId_fkey` FOREIGN KEY (`winnerId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Product` ADD CONSTRAINT `Product_winnerBidId_fkey` FOREIGN KEY (`winnerBidId`) REFERENCES `Bid`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Order` ADD CONSTRAINT `Order_bidId_fkey` FOREIGN KEY (`bidId`) REFERENCES `Bid`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
