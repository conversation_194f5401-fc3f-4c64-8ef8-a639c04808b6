-- CreateTable
CREATE TABLE `AuctionExtensionLog` (
    `id` VARCHAR(191) NOT NULL,
    `productId` VARCHAR(191) NOT NULL,
    `previousEndDate` DATETIME(3) NOT NULL,
    `newEndDate` DATETIME(3) NOT NULL,
    `extendedMinutes` INTEGER NOT NULL,
    `triggerBidAmount` DECIMAL(15, 2) NOT NULL,
    `triggeredBy` VARCHAR(191) NOT NULL,
    `triggeredBidderId` VARCHAR(191) NULL,
    `extensionReason` VARCHAR(191) NOT NULL DEFAULT 'bid_in_final_minutes',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `AuctionExtensionLog_productId_idx`(`productId`),
    INDEX `AuctionExtensionLog_createdAt_idx`(`createdAt`),
    INDEX `AuctionExtensionLog_triggeredBidderId_idx`(`triggeredBidderId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `AuctionExtensionLog` ADD CONSTRAINT `AuctionExtensionLog_productId_fkey` FOREIGN KEY (`productId`) REFERENCES `Product`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `AuctionExtensionLog` ADD CONSTRAINT `AuctionExtensionLog_triggeredBidderId_fkey` FOREIGN KEY (`triggeredBidderId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
