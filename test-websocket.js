const WebSocket = require('ws');

// Test WebSocket connection and bidding
async function testWebSocket() {
    console.log('🧪 Starting WebSocket test...');
    
    const ws = new WebSocket('ws://localhost:3001');
    
    ws.on('open', function open() {
        console.log('✅ WebSocket connected');
        
        // Test authentication (optional - replace with real token)
        const authMessage = {
            type: 'authenticate',
            userId: 'test-user-123',
            token: 'test-token-123'
        };
        
        console.log('🔐 Sending authentication...');
        ws.send(JSON.stringify(authMessage));
        
        // Subscribe to a test product
        setTimeout(() => {
            const subscribeMessage = {
                type: 'subscribe',
                channel: 'product:test-product-id'
            };
            
            console.log('🔔 Subscribing to product channel...');
            ws.send(JSON.stringify(subscribeMessage));
        }, 1000);
        
        // Test ping
        setTimeout(() => {
            const pingMessage = {
                type: 'ping',
                timestamp: new Date().toISOString()
            };
            
            console.log('🏓 Sending ping...');
            ws.send(JSON.stringify(pingMessage));
        }, 2000);
        
        // Get stats
        setTimeout(() => {
            const statsMessage = {
                type: 'get_stats'
            };
            
            console.log('📊 Requesting stats...');
            ws.send(JSON.stringify(statsMessage));
        }, 3000);
    });
    
    ws.on('message', function message(data) {
        try {
            const parsed = JSON.parse(data.toString());
            console.log('📨 Received message:', {
                type: parsed.type,
                channel: parsed.channel,
                message: parsed.message,
                data: parsed.data ? 'Data present' : 'No data'
            });
            
            if (parsed.type === 'broadcast') {
                console.log('📡 Broadcast received:', parsed);
            }
        } catch (error) {
            console.error('❌ Error parsing message:', error);
            console.log('Raw message:', data.toString());
        }
    });
    
    ws.on('error', function error(err) {
        console.error('❌ WebSocket error:', err);
    });
    
    ws.on('close', function close(code, reason) {
        console.log('🔌 WebSocket closed:', { code, reason: reason.toString() });
    });
    
    // Keep connection alive for testing
    setTimeout(() => {
        console.log('🛑 Closing test connection...');
        ws.close();
    }, 10000);
}

// Run test
testWebSocket().catch(console.error);
